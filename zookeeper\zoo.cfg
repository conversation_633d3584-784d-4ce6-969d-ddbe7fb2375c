# ZooKeeper 3.8.4 集群配置文件
# 启用SASL认证的集群模式配置

# 基本配置
tickTime=2000
initLimit=10
syncLimit=5

# 数据目录
dataDir=/export/data/zookeeper
dataLogDir=/export/logs/zookeeper

# 客户端连接端口
clientPort=4181

# 管理员服务器配置
admin.enableServer=true
admin.serverPort=8080

# 集群配置
# 格式: server.id=host:port:port
server.1=***************:2888:3888
server.2=***************:2888:3888
server.3=***************:2888:3888

# SASL认证配置（3.8.4版本）
authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
sessionRequireClientSASLAuth=true
jaasLoginRenew=3600000

# 审计和安全配置
audit.enable=true

# 集群间SASL认证配置
quorum.auth.enableSasl=true
quorum.auth.learnerRequireSasl=true
quorum.auth.serverRequireSasl=true
quorum.auth.learnerLoginContext=QuorumLearner
quorum.auth.serverLoginContext=QuorumServer

# 网络配置
serverCnxnFactory=org.apache.zookeeper.server.NettyServerCnxnFactory
maxClientCnxns=60
quorum.cnxn.threads.size=20

# 性能调优配置
autopurge.snapRetainCount=3
autopurge.purgeInterval=1

# 快照和事务日志配置
preAllocSize=65536
snapCount=100000

# 会话超时配置
minSessionTimeout=4000
maxSessionTimeout=40000

# 4lw命令配置（用于监控）
4lw.commands.whitelist=stat,ruok,conf,isro,srvr,mntr

# 日志配置
zookeeper.log.threshold=INFO