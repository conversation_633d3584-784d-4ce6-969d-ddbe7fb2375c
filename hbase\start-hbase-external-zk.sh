#!/bin/bash

# HBase启动脚本 - 使用外置ZooKeeper

echo "=== 启动HBase（使用外置ZooKeeper）==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo ""
echo "1. 检查外置ZooKeeper集群状态..."

# 检查ZooKeeper集群
zk_running=0
for server in 192.168.200.101 192.168.200.102 192.168.200.103; do
    if echo ruok | nc -w3 $server 4181 2>/dev/null | grep -q imok; then
        echo -e "  ${GREEN}✓${NC} ZooKeeper $server:4181 运行正常"
        ((zk_running++))
    else
        echo -e "  ${RED}✗${NC} ZooKeeper $server:4181 连接失败"
    fi
done

if [ $zk_running -eq 0 ]; then
    echo -e "${RED}错误: 外置ZooKeeper集群不可用${NC}"
    echo "请先启动ZooKeeper集群: ./zookeeper/start-zookeeper.sh"
    exit 1
elif [ $zk_running -lt 2 ]; then
    echo -e "${YELLOW}警告: ZooKeeper集群节点不足 ($zk_running/3)${NC}"
else
    echo -e "${GREEN}✓ ZooKeeper集群状态良好 ($zk_running/3 节点运行)${NC}"
fi

echo ""
echo "2. 停止现有HBase进程..."

# 停止可能运行的HBase进程
if ps aux | grep -q "[H]Master"; then
    echo "  停止HBase Master..."
    /export/server/hbase/hbase/bin/hbase-daemon.sh stop master 2>/dev/null
    sleep 5
    
    # 强制终止如果还在运行
    if ps aux | grep -q "[H]Master"; then
        pkill -f HMaster
        sleep 3
    fi
fi

echo ""
echo "3. 复制配置文件..."

# 复制配置文件到HBase安装目录
if [ -d "/export/server/hbase/hbase/conf" ]; then
    echo "  复制配置文件到HBase安装目录..."
    cp hbase/conf/* /export/server/hbase/hbase/conf/
    echo -e "  ${GREEN}✓${NC} 配置文件复制完成"
else
    echo -e "  ${RED}✗${NC} HBase配置目录不存在: /export/server/hbase/hbase/conf"
    exit 1
fi

echo ""
echo "4. 验证关键配置..."

# 检查分布式模式配置
if grep -q "hbase.cluster.distributed.*true" /export/server/hbase/hbase/conf/hbase-site.xml; then
    echo -e "  ${GREEN}✓${NC} 分布式模式已启用"
else
    echo -e "  ${RED}✗${NC} 分布式模式未启用"
    exit 1
fi

# 检查ZooKeeper配置
if grep -q "192.168.200.101,192.168.200.102,192.168.200.103" /export/server/hbase/hbase/conf/hbase-site.xml; then
    echo -e "  ${GREEN}✓${NC} 外置ZooKeeper配置正确"
else
    echo -e "  ${RED}✗${NC} 外置ZooKeeper配置错误"
    exit 1
fi

# 检查SASL配置
if [ -f "/export/server/hbase/hbase/conf/jaas.conf" ]; then
    echo -e "  ${GREEN}✓${NC} SASL认证配置文件存在"
else
    echo -e "  ${YELLOW}⚠${NC} SASL认证配置文件不存在"
fi

echo ""
echo "5. 创建必要目录..."

# 创建目录
mkdir -p /export/logs/hbase
mkdir -p /export/data/hbase
mkdir -p /export/server/hbase/hbase/pids

# 设置权限
chown -R root:root /export/logs/hbase /export/data/hbase /export/server/hbase/hbase/pids
chmod 755 /export/logs/hbase /export/data/hbase /export/server/hbase/hbase/pids

echo -e "  ${GREEN}✓${NC} 目录创建完成"

echo ""
echo "6. 设置环境变量..."

# 设置环境变量
export JAVA_HOME=/export/tools/jdk8
export HBASE_HOME=/export/server/hbase/hbase
export HBASE_CONF_DIR=/export/server/hbase/hbase/conf

echo "  JAVA_HOME: $JAVA_HOME"
echo "  HBASE_HOME: $HBASE_HOME"
echo "  HBASE_CONF_DIR: $HBASE_CONF_DIR"

echo ""
echo "7. 启动HBase Master..."

# 启动HBase Master
echo "  正在启动HBase Master..."
/export/server/hbase/hbase/bin/hbase-daemon.sh start master

# 等待启动
echo "  等待HBase启动..."
sleep 20

echo ""
echo "8. 验证启动状态..."

# 检查进程
if ps aux | grep -q "[H]Master"; then
    echo -e "  ${GREEN}✓${NC} HBase Master进程运行中"
    
    # 获取进程信息
    master_info=$(ps aux | grep "[H]Master" | head -1)
    echo "    进程信息: $(echo $master_info | awk '{print $2, $11}')"
else
    echo -e "  ${RED}✗${NC} HBase Master进程未运行"
    echo ""
    echo "  检查启动日志:"
    latest_log=$(ls -t /export/logs/hbase/hbase-root-master-*.out 2>/dev/null | head -1)
    if [ -n "$latest_log" ]; then
        echo "  最新日志: $latest_log"
        echo "  错误信息:"
        tail -10 "$latest_log" | grep -E "(ERROR|Exception|Failed)" | tail -3 | sed 's/^/    /'
    fi
    exit 1
fi

# 检查端口监听
echo "  检查端口监听..."
sleep 5

if netstat -tlnp 2>/dev/null | grep -q ":16010"; then
    echo -e "    ${GREEN}✓${NC} Web UI端口16010已监听"
else
    echo -e "    ${YELLOW}⚠${NC} Web UI端口16010未监听（可能仍在启动中）"
fi

if netstat -tlnp 2>/dev/null | grep -q ":16000"; then
    echo -e "    ${GREEN}✓${NC} Master端口16000已监听"
else
    echo -e "    ${YELLOW}⚠${NC} Master端口16000未监听（可能仍在启动中）"
fi

echo ""
echo "9. 检查日志..."

# 检查启动日志
latest_out=$(ls -t /export/logs/hbase/hbase-root-master-*.out 2>/dev/null | head -1)
if [ -n "$latest_out" ]; then
    echo "  检查启动日志: $latest_out"
    
    # 检查是否有错误
    if tail -20 "$latest_out" | grep -qi "error\|exception\|failed"; then
        echo -e "    ${YELLOW}⚠${NC} 发现错误信息:"
        tail -20 "$latest_out" | grep -i "error\|exception\|failed" | tail -3 | sed 's/^/      /'
    else
        echo -e "    ${GREEN}✓${NC} 未发现明显错误"
    fi
    
    # 显示最新几行
    echo "    最新日志:"
    tail -3 "$latest_out" | sed 's/^/      /'
fi

echo ""
echo "10. 测试连接..."

# 等待一段时间让HBase完全启动
echo "  等待HBase完全初始化..."
sleep 10

# 测试HBase Shell连接
echo "  测试HBase Shell连接..."
timeout 15 /export/server/hbase/hbase/bin/hbase shell <<< "list" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "  ${GREEN}✓${NC} HBase Shell连接成功"
else
    echo -e "  ${YELLOW}⚠${NC} HBase Shell连接测试超时（HBase可能仍在初始化）"
fi

echo ""
echo "=== HBase启动完成 ==="
echo ""
echo "服务信息:"
echo "  Web UI: http://192.168.200.101:16010"
echo "  Master: 192.168.200.101:16000"
echo "  Shell: /export/server/hbase/hbase/bin/hbase shell"
echo ""
echo "监控命令:"
echo "  查看进程: ps aux | grep HMaster"
echo "  查看端口: netstat -tlnp | grep -E '16000|16010'"
echo "  查看日志: tail -f /export/logs/hbase/hbase-root-master-*.out"
echo ""
echo "如果启动失败，请检查:"
echo "  1. ZooKeeper集群是否正常运行"
echo "  2. 网络连接是否正常"
echo "  3. HBase日志中的错误信息"
