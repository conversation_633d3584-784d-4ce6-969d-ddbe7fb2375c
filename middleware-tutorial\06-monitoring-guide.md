# 监控运维指南

## 监控架构设计

### 1. 监控体系概览

```mermaid
graph TB
    subgraph "数据收集层"
        A1[Node Exporter] --> A2[Metrics]
        B1[JMX Exporter] --> A2
        C1[Log Agents] --> C2[Logs]
        D1[APM Agents] --> D2[Traces]
    end
    
    subgraph "数据存储层"
        A2 --> E1[Prometheus]
        C2 --> E2[Elasticsearch]
        D2 --> E3[Jae<PERSON>]
    end
    
    subgraph "可视化层"
        E1 --> F1[Grafana]
        E2 --> F2[Kibana]
        E3 --> F3[Jaeger UI]
    end
    
    subgraph "告警层"
        E1 --> G1[AlertManager]
        G1 --> G2[通知渠道]
    end
```

### 2. 监控指标分类

#### **基础设施监控**
```
系统指标:
- CPU使用率、负载
- 内存使用率、交换分区
- 磁盘使用率、IOPS
- 网络流量、连接数

硬件指标:
- 磁盘健康状态
- 温度传感器
- 电源状态
- 风扇转速
```

#### **应用监控**
```
JVM指标:
- 堆内存使用
- GC频率和耗时
- 线程数量
- 类加载数量

业务指标:
- 请求QPS
- 响应时间
- 错误率
- 业务成功率
```

#### **中间件监控**
```
Kafka指标:
- 消息生产/消费速率
- 分区分布
- 消费者滞后
- 副本同步状态

Elasticsearch指标:
- 索引速率
- 查询QPS
- 集群健康状态
- 分片分布

ZooKeeper指标:
- 连接数
- 延迟时间
- 选举状态
- 数据大小
```

## Prometheus监控配置

### 1. Prometheus服务器配置

#### **prometheus.yml完整配置**
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'bigdata-cluster'
    region: 'beijing'

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: 
        - '***************:9100'
        - '***************:9100'
        - '***************:9100'
    scrape_interval: 30s
    metrics_path: /metrics

  # JVM监控
  - job_name: 'jvm-kafka'
    static_configs:
      - targets:
        - '***************:9308'
        - '***************:9308'
        - '***************:9308'
    scrape_interval: 30s
    metrics_path: /metrics

  # Kafka监控
  - job_name: 'kafka-exporter'
    static_configs:
      - targets:
        - '***************:9309'
        - '***************:9309'
        - '***************:9309'
    scrape_interval: 30s

  # Elasticsearch监控
  - job_name: 'elasticsearch-exporter'
    static_configs:
      - targets:
        - '***************:9114'
        - '***************:9114'
        - '***************:9114'
    scrape_interval: 30s

  # ZooKeeper监控
  - job_name: 'zookeeper-exporter'
    static_configs:
      - targets:
        - '***************:9141'
        - '***************:9141'
        - '***************:9141'
    scrape_interval: 30s

  # 自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
```

### 2. JMX Exporter配置

#### **Kafka JMX配置**
```yaml
# kafka-jmx-config.yml
rules:
  # Broker指标
  - pattern: kafka.server<type=BrokerTopicMetrics, name=MessagesInPerSec><>Count
    name: kafka_broker_messages_in_total
    type: COUNTER
    labels:
      topic: "$1"

  - pattern: kafka.server<type=BrokerTopicMetrics, name=BytesInPerSec><>Count
    name: kafka_broker_bytes_in_total
    type: COUNTER

  - pattern: kafka.server<type=BrokerTopicMetrics, name=BytesOutPerSec><>Count
    name: kafka_broker_bytes_out_total
    type: COUNTER

  # 网络指标
  - pattern: kafka.network<type=RequestMetrics, name=TotalTimeMs, request=(.+)><>Mean
    name: kafka_network_request_total_time_ms
    type: GAUGE
    labels:
      request: "$1"

  # 日志指标
  - pattern: kafka.log<type=LogFlushStats, name=LogFlushRateAndTimeMs><>Count
    name: kafka_log_flush_total
    type: COUNTER

  # 副本指标
  - pattern: kafka.server<type=ReplicaManager, name=(.+)><>Value
    name: kafka_replica_manager_$1
    type: GAUGE

  # 控制器指标
  - pattern: kafka.controller<type=KafkaController, name=(.+)><>Value
    name: kafka_controller_$1
    type: GAUGE
```

#### **Elasticsearch JMX配置**
```yaml
# elasticsearch-jmx-config.yml
rules:
  # JVM内存指标
  - pattern: 'java.lang<type=Memory><HeapMemoryUsage>used'
    name: jvm_memory_heap_used_bytes
    type: GAUGE

  - pattern: 'java.lang<type=Memory><NonHeapMemoryUsage>used'
    name: jvm_memory_nonheap_used_bytes
    type: GAUGE

  # GC指标
  - pattern: 'java.lang<type=GarbageCollector, name=(.+)><CollectionCount>'
    name: jvm_gc_collection_total
    type: COUNTER
    labels:
      gc: "$1"

  - pattern: 'java.lang<type=GarbageCollector, name=(.+)><CollectionTime>'
    name: jvm_gc_collection_time_ms
    type: COUNTER
    labels:
      gc: "$1"

  # 线程指标
  - pattern: 'java.lang<type=Threading><ThreadCount>'
    name: jvm_threads_current
    type: GAUGE

  - pattern: 'java.lang<type=Threading><DaemonThreadCount>'
    name: jvm_threads_daemon
    type: GAUGE
```

### 3. 告警规则配置

#### **Kafka告警规则**
```yaml
# kafka-alerts.yml
groups:
  - name: kafka.rules
    rules:
      # 服务可用性
      - alert: KafkaDown
        expr: up{job="kafka-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: kafka
        annotations:
          summary: "Kafka broker is down"
          description: "Kafka broker on {{ $labels.instance }} has been down for more than 1 minute."

      # 消息积压
      - alert: KafkaConsumerLag
        expr: kafka_consumer_lag_sum > 10000
        for: 5m
        labels:
          severity: warning
          service: kafka
        annotations:
          summary: "Kafka consumer lag is high"
          description: "Consumer group {{ $labels.group }} has lag of {{ $value }} messages on topic {{ $labels.topic }}"

      # 磁盘使用率
      - alert: KafkaDiskUsageHigh
        expr: (kafka_log_size / kafka_log_size_limit) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: kafka
        annotations:
          summary: "Kafka disk usage is high"
          description: "Kafka disk usage is {{ $value }}% on {{ $labels.instance }}"

      # 网络延迟
      - alert: KafkaHighLatency
        expr: kafka_network_request_total_time_ms{quantile="0.99"} > 1000
        for: 5m
        labels:
          severity: warning
          service: kafka
        annotations:
          summary: "Kafka request latency is high"
          description: "99th percentile latency is {{ $value }}ms on {{ $labels.instance }}"

      # 副本同步
      - alert: KafkaUnderReplicatedPartitions
        expr: kafka_server_replica_manager_under_replicated_partitions > 0
        for: 2m
        labels:
          severity: critical
          service: kafka
        annotations:
          summary: "Kafka has under-replicated partitions"
          description: "{{ $value }} partitions are under-replicated on {{ $labels.instance }}"
```

#### **Elasticsearch告警规则**
```yaml
# elasticsearch-alerts.yml
groups:
  - name: elasticsearch.rules
    rules:
      # 集群状态
      - alert: ElasticsearchClusterRed
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        for: 1m
        labels:
          severity: critical
          service: elasticsearch
        annotations:
          summary: "Elasticsearch cluster status is red"
          description: "Elasticsearch cluster {{ $labels.cluster }} status is red"

      - alert: ElasticsearchClusterYellow
        expr: elasticsearch_cluster_health_status{color="yellow"} == 1
        for: 5m
        labels:
          severity: warning
          service: elasticsearch
        annotations:
          summary: "Elasticsearch cluster status is yellow"
          description: "Elasticsearch cluster {{ $labels.cluster }} status is yellow for more than 5 minutes"

      # 节点状态
      - alert: ElasticsearchNodeDown
        expr: up{job="elasticsearch-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: elasticsearch
        annotations:
          summary: "Elasticsearch node is down"
          description: "Elasticsearch node {{ $labels.instance }} has been down for more than 1 minute"

      # 磁盘空间
      - alert: ElasticsearchDiskSpaceLow
        expr: elasticsearch_filesystem_data_available_bytes / elasticsearch_filesystem_data_size_bytes * 100 < 15
        for: 5m
        labels:
          severity: warning
          service: elasticsearch
        annotations:
          summary: "Elasticsearch disk space is low"
          description: "Disk space is {{ $value }}% available on {{ $labels.instance }}"

      # JVM堆内存
      - alert: ElasticsearchHeapUsageHigh
        expr: elasticsearch_jvm_memory_used_bytes{area="heap"} / elasticsearch_jvm_memory_max_bytes{area="heap"} * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: elasticsearch
        annotations:
          summary: "Elasticsearch heap usage is high"
          description: "JVM heap usage is {{ $value }}% on {{ $labels.instance }}"

      # 查询性能
      - alert: ElasticsearchQueryLatencyHigh
        expr: elasticsearch_indices_search_query_time_seconds / elasticsearch_indices_search_query_total > 1
        for: 5m
        labels:
          severity: warning
          service: elasticsearch
        annotations:
          summary: "Elasticsearch query latency is high"
          description: "Average query latency is {{ $value }}s on {{ $labels.instance }}"
```

## Grafana仪表板

### 1. Kafka仪表板

#### **关键指标面板**
```json
{
  "dashboard": {
    "title": "Kafka Cluster Overview",
    "panels": [
      {
        "title": "Broker Status",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"kafka-exporter\"}",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "Messages In Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(kafka_broker_messages_in_total[5m])",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "Bytes In/Out Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(kafka_broker_bytes_in_total[5m])",
            "legendFormat": "In - {{instance}}"
          },
          {
            "expr": "rate(kafka_broker_bytes_out_total[5m])",
            "legendFormat": "Out - {{instance}}"
          }
        ]
      },
      {
        "title": "Consumer Lag",
        "type": "graph",
        "targets": [
          {
            "expr": "kafka_consumer_lag_sum",
            "legendFormat": "{{group}} - {{topic}}"
          }
        ]
      },
      {
        "title": "Request Latency (99th percentile)",
        "type": "graph",
        "targets": [
          {
            "expr": "kafka_network_request_total_time_ms{quantile=\"0.99\"}",
            "legendFormat": "{{request}} - {{instance}}"
          }
        ]
      }
    ]
  }
}
```

### 2. Elasticsearch仪表板

#### **集群概览面板**
```json
{
  "dashboard": {
    "title": "Elasticsearch Cluster Overview",
    "panels": [
      {
        "title": "Cluster Health",
        "type": "stat",
        "targets": [
          {
            "expr": "elasticsearch_cluster_health_status",
            "legendFormat": "{{color}}"
          }
        ]
      },
      {
        "title": "Active Nodes",
        "type": "stat",
        "targets": [
          {
            "expr": "elasticsearch_cluster_health_number_of_nodes"
          }
        ]
      },
      {
        "title": "Index Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(elasticsearch_indices_indexing_index_total[5m])",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "Search Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(elasticsearch_indices_search_query_total[5m])",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "JVM Heap Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "elasticsearch_jvm_memory_used_bytes{area=\"heap\"} / elasticsearch_jvm_memory_max_bytes{area=\"heap\"} * 100",
            "legendFormat": "{{instance}}"
          }
        ]
      }
    ]
  }
}
```

## 日志监控

### 1. 日志收集配置

#### **Filebeat配置**
```yaml
# filebeat.yml
filebeat.inputs:
  # Kafka日志
  - type: log
    paths:
      - /export/logs/kafka/*.log
    fields:
      service: kafka
      log_type: application
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after

  # Elasticsearch日志
  - type: log
    paths:
      - /export/logs/elasticsearch/*.log
    fields:
      service: elasticsearch
      log_type: application
    json.keys_under_root: true
    json.add_error_key: true

  # 系统日志
  - type: log
    paths:
      - /var/log/syslog
      - /var/log/messages
    fields:
      service: system
      log_type: system

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~

output.elasticsearch:
  hosts: ["***************:9200", "***************:9200", "***************:9200"]
  index: "logs-%{[fields.service]}-%{+yyyy.MM.dd}"
  template.name: "logs"
  template.pattern: "logs-*"

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
```

### 2. 日志分析规则

#### **Logstash解析规则**
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "kafka" {
    grok {
      match => { 
        "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\] %{LOGLEVEL:level} %{GREEDYDATA:msg} \(%{DATA:class}\)"
      }
    }
    
    if [level] == "ERROR" or [level] == "WARN" {
      mutate {
        add_tag => ["alert"]
      }
    }
  }
  
  if [fields][service] == "elasticsearch" {
    if [message] =~ /^\{/ {
      json {
        source => "message"
      }
    } else {
      grok {
        match => {
          "message" => "\[%{TIMESTAMP_ISO8601:timestamp}\]\[%{LOGLEVEL:level}\s*\]\[%{DATA:component}\] %{GREEDYDATA:msg}"
        }
      }
    }
  }
  
  date {
    match => [ "timestamp", "yyyy-MM-dd HH:mm:ss,SSS", "ISO8601" ]
  }
  
  mutate {
    remove_field => ["@version", "beat", "input", "prospector"]
  }
}

output {
  elasticsearch {
    hosts => ["***************:9200"]
    index => "logs-%{[fields][service]}-%{+YYYY.MM.dd}"
  }
  
  if "alert" in [tags] {
    email {
      to => "<EMAIL>"
      subject => "Alert: %{[fields][service]} %{level} on %{host}"
      body => "Message: %{message}\nHost: %{host}\nTime: %{@timestamp}"
    }
  }
}
```

## 性能监控

### 1. 应用性能监控(APM)

#### **Elastic APM配置**
```yaml
# apm-server.yml
apm-server:
  host: "0.0.0.0:8200"
  
  rum:
    enabled: true
    event_rate.limit: 300
    event_rate.lru_size: 1000
    allow_origins: ['*']
    
  register.ingest.pipeline.enabled: true
  
output.elasticsearch:
  hosts: ["***************:9200", "***************:9200", "***************:9200"]
  indices:
    - index: "apm-%{[processor.event]}-%{+yyyy.MM.dd}"

setup.template.name: "apm"
setup.template.pattern: "apm-*"

logging.level: info
```

### 2. 自定义指标收集

#### **业务指标收集器**
```python
# custom_metrics_collector.py
import time
import requests
from prometheus_client import start_http_server, Gauge, Counter, Histogram

# 定义指标
kafka_lag_gauge = Gauge('kafka_consumer_lag_custom', 'Consumer lag', ['topic', 'group'])
es_query_duration = Histogram('elasticsearch_query_duration_seconds', 'Query duration')
business_counter = Counter('business_operations_total', 'Business operations', ['operation', 'status'])

class MetricsCollector:
    def __init__(self):
        self.kafka_admin_url = "http://***************:9092"
        self.es_url = "http://***************:9200"
    
    def collect_kafka_metrics(self):
        # 收集Kafka消费者滞后信息
        try:
            # 这里应该使用Kafka Admin API获取真实数据
            lag_data = self.get_consumer_lag()
            for topic, groups in lag_data.items():
                for group, lag in groups.items():
                    kafka_lag_gauge.labels(topic=topic, group=group).set(lag)
        except Exception as e:
            print(f"Error collecting Kafka metrics: {e}")
    
    def collect_es_metrics(self):
        # 收集Elasticsearch查询性能
        try:
            start_time = time.time()
            response = requests.get(f"{self.es_url}/_cluster/health")
            duration = time.time() - start_time
            es_query_duration.observe(duration)
        except Exception as e:
            print(f"Error collecting ES metrics: {e}")
    
    def collect_business_metrics(self):
        # 收集业务指标
        try:
            # 这里应该从业务系统获取真实数据
            operations = self.get_business_operations()
            for op in operations:
                business_counter.labels(
                    operation=op['type'], 
                    status=op['status']
                ).inc()
        except Exception as e:
            print(f"Error collecting business metrics: {e}")
    
    def run(self):
        while True:
            self.collect_kafka_metrics()
            self.collect_es_metrics()
            self.collect_business_metrics()
            time.sleep(30)

if __name__ == '__main__':
    start_http_server(8000)
    collector = MetricsCollector()
    collector.run()
```

## 总结

完善的监控体系应该包括：

1. **多层次监控**: 基础设施、中间件、应用、业务
2. **实时告警**: 及时发现和响应问题
3. **可视化展示**: 直观的仪表板和图表
4. **日志聚合**: 集中化的日志管理和分析
5. **性能追踪**: 端到端的性能监控
6. **自动化运维**: 基于监控数据的自动化响应

通过这套监控体系，可以实现对整个大数据平台的全面监控和运维。
