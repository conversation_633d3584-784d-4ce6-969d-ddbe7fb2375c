# Kafka 4.0生产者配置文件
# 包含SASL/PLAIN认证

# Bootstrap服务器列表
bootstrap.servers=192.168.200.101:9092

# SASL认证配置
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="producer" password="producer123";

# 键序列化器
key.serializer=org.apache.kafka.common.serialization.StringSerializer

# 值序列化器
value.serializer=org.apache.kafka.common.serialization.StringSerializer

# 确认模式（all表示所有副本都确认）
acks=all

# 重试次数
retries=2147483647

# 批处理大小
batch.size=16384

# 延迟时间
linger.ms=0

# 缓冲区内存大小
buffer.memory=33554432

# 压缩类型
compression.type=none

# 最大请求大小
max.request.size=1048576

# 连接最大空闲时间
connections.max.idle.ms=540000

# 请求超时时间
request.timeout.ms=30000

# 重试间隔
retry.backoff.ms=100

# 重连间隔
reconnect.backoff.ms=50

# 最大重连间隔
reconnect.backoff.max.ms=1000

# 最大阻塞时间
max.block.ms=60000

# 元数据最大存活时间
metadata.max.age.ms=300000

# 发送缓冲区大小
send.buffer.bytes=131072

# 接收缓冲区大小
receive.buffer.bytes=32768

# 幂等性配置
enable.idempotence=false

# 最大飞行请求数
max.in.flight.requests.per.connection=5

# 事务超时时间
transaction.timeout.ms=60000
