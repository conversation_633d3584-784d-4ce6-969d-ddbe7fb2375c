#!/bin/bash

# Elasticsearch停止脚本

echo "=== 停止Elasticsearch ==="

# 检查Elasticsearch进程
echo "1. 检查Elasticsearch进程..."
es_pids=$(pgrep -f "org.elasticsearch.bootstrap.Elasticsearch")

if [ -z "$es_pids" ]; then
    echo "✓ 没有发现Elasticsearch进程"
    exit 0
else
    echo "发现Elasticsearch进程: $es_pids"
fi

# 优雅停止
echo ""
echo "2. 尝试优雅停止..."
for pid in $es_pids; do
    echo "停止进程 $pid..."
    kill $pid
done

# 等待进程停止
echo "等待进程停止..."
sleep 15

# 检查进程是否停止
remaining_pids=$(pgrep -f "org.elasticsearch.bootstrap.Elasticsearch")
if [ -z "$remaining_pids" ]; then
    echo "✓ Elasticsearch进程已优雅停止"
else
    echo "⚠ 仍有进程运行: $remaining_pids"
    echo ""
    echo "3. 强制停止残留进程..."
    
    for pid in $remaining_pids; do
        echo "强制停止进程 $pid..."
        kill -9 $pid
    done
    
    sleep 5
    
    # 最终检查
    final_check=$(pgrep -f "org.elasticsearch.bootstrap.Elasticsearch")
    if [ -z "$final_check" ]; then
        echo "✓ 所有Elasticsearch进程已强制停止"
    else
        echo "✗ 仍有进程无法停止: $final_check"
        echo "请手动处理这些进程"
    fi
fi

# 检查端口释放
echo ""
echo "4. 检查端口释放..."
if netstat -tlnp | grep -q ":9200"; then
    echo "⚠ 端口9200仍被占用"
    netstat -tlnp | grep ":9200"
else
    echo "✓ 端口9200已释放"
fi

if netstat -tlnp | grep -q ":9300"; then
    echo "⚠ 端口9300仍被占用"
    netstat -tlnp | grep ":9300"
else
    echo "✓ 端口9300已释放"
fi

echo ""
echo "=== Elasticsearch停止完成 ==="
