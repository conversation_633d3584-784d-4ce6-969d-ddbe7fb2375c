#!/bin/bash

# HBase状态检查脚本

echo "=== HBase启动状态检查 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查结果统计
checks_passed=0
total_checks=0

# 检查函数
check_result() {
    local result=$1
    local message=$2
    ((total_checks++))
    
    if [ $result -eq 0 ]; then
        echo -e "  ${GREEN}✓${NC} $message"
        ((checks_passed++))
    else
        echo -e "  ${RED}✗${NC} $message"
    fi
}

echo ""
echo "1. 检查HBase进程..."

# 检查HMaster进程
if ps aux | grep -q "[H]Master"; then
    master_pid=$(ps aux | grep "[H]Master" | awk '{print $2}' | head -1)
    check_result 0 "HBase Master进程运行中 (PID: $master_pid)"
    
    # 显示进程详细信息
    master_info=$(ps aux | grep "[H]Master" | head -1)
    echo "    进程信息: $(echo $master_info | awk '{print $1, $2, $9, $10, $11}')"
else
    check_result 1 "HBase Master进程未运行"
fi

# 检查HRegionServer进程
if ps aux | grep -q "[H]RegionServer"; then
    regionserver_pid=$(ps aux | grep "[H]RegionServer" | awk '{print $2}' | head -1)
    check_result 0 "HBase RegionServer进程运行中 (PID: $regionserver_pid)"
else
    check_result 1 "HBase RegionServer进程未运行（单机模式下可能正常）"
fi

echo ""
echo "2. 检查端口监听..."

# 检查Master RPC端口
if netstat -tlnp 2>/dev/null | grep -q ":16000"; then
    check_result 0 "Master RPC端口16000已监听"
else
    check_result 1 "Master RPC端口16000未监听"
fi

# 检查Master Web UI端口
if netstat -tlnp 2>/dev/null | grep -q ":16010"; then
    check_result 0 "Master Web UI端口16010已监听"
else
    check_result 1 "Master Web UI端口16010未监听"
fi

# 检查RegionServer端口
if netstat -tlnp 2>/dev/null | grep -q ":16020"; then
    check_result 0 "RegionServer RPC端口16020已监听"
else
    check_result 1 "RegionServer RPC端口16020未监听（单机模式下可能正常）"
fi

if netstat -tlnp 2>/dev/null | grep -q ":16030"; then
    check_result 0 "RegionServer Web UI端口16030已监听"
else
    check_result 1 "RegionServer Web UI端口16030未监听（单机模式下可能正常）"
fi

echo ""
echo "3. 检查Web界面..."

# 检查Master Web UI
if curl -s --connect-timeout 5 http://192.168.200.101:16010 >/dev/null 2>&1; then
    check_result 0 "Master Web UI可访问 (http://192.168.200.101:16010)"
else
    check_result 1 "Master Web UI不可访问"
fi

echo ""
echo "4. 检查ZooKeeper连接..."

# 检查ZooKeeper集群
zk_connected=0
for server in 192.168.200.101 192.168.200.102 192.168.200.103; do
    if echo ruok | nc -w3 $server 4181 2>/dev/null | grep -q imok; then
        ((zk_connected++))
    fi
done

if [ $zk_connected -gt 0 ]; then
    check_result 0 "ZooKeeper集群连接正常 ($zk_connected/3 节点)"
else
    check_result 1 "ZooKeeper集群连接失败"
fi

echo ""
echo "5. 检查日志文件..."

# 检查启动日志
latest_out=$(ls -t /export/logs/hbase/hbase-root-master-*.out 2>/dev/null | head -1)
if [ -n "$latest_out" ]; then
    check_result 0 "启动日志文件存在: $latest_out"
    
    # 检查日志中的错误
    error_count=$(tail -50 "$latest_out" | grep -ci "error\|exception\|failed")
    if [ $error_count -eq 0 ]; then
        check_result 0 "启动日志中无明显错误"
    else
        check_result 1 "启动日志中发现 $error_count 个错误"
        echo "    最新错误:"
        tail -50 "$latest_out" | grep -i "error\|exception\|failed" | tail -3 | sed 's/^/      /'
    fi
else
    check_result 1 "启动日志文件不存在"
fi

# 检查主日志文件
if [ -f "/export/logs/hbase/hbase.log" ]; then
    check_result 0 "主日志文件存在: /export/logs/hbase/hbase.log"
else
    check_result 1 "主日志文件不存在"
fi

echo ""
echo "6. 测试HBase Shell连接..."

# 测试HBase Shell
echo "  正在测试HBase Shell连接..."
timeout 15 /export/server/hbase/hbase/bin/hbase shell <<< "list" >/dev/null 2>&1
if [ $? -eq 0 ]; then
    check_result 0 "HBase Shell连接成功"
else
    check_result 1 "HBase Shell连接失败或超时"
fi

echo ""
echo "7. 检查HBase版本和配置..."

# 检查HBase版本
if [ -f "/export/server/hbase/hbase/bin/hbase" ]; then
    hbase_version=$(/export/server/hbase/hbase/bin/hbase version 2>/dev/null | head -1)
    if [ -n "$hbase_version" ]; then
        check_result 0 "HBase版本: $hbase_version"
    else
        check_result 1 "无法获取HBase版本信息"
    fi
else
    check_result 1 "HBase可执行文件不存在"
fi

# 检查关键配置
if [ -f "/export/server/hbase/hbase/conf/hbase-site.xml" ]; then
    if grep -q "hbase.cluster.distributed.*true" /export/server/hbase/hbase/conf/hbase-site.xml; then
        check_result 0 "分布式模式配置正确"
    else
        check_result 1 "分布式模式配置错误"
    fi
else
    check_result 1 "HBase配置文件不存在"
fi

echo ""
echo "=== 检查结果汇总 ==="

# 计算成功率
success_rate=$((checks_passed * 100 / total_checks))

echo "总检查项: $total_checks"
echo "通过检查: $checks_passed"
echo "成功率: $success_rate%"

if [ $success_rate -ge 80 ]; then
    echo -e "${GREEN}✓ HBase启动状态良好${NC}"
elif [ $success_rate -ge 60 ]; then
    echo -e "${YELLOW}⚠ HBase部分功能可能有问题${NC}"
else
    echo -e "${RED}✗ HBase启动失败或存在严重问题${NC}"
fi

echo ""
echo "=== 快速访问信息 ==="
echo "Web界面:"
echo "  Master UI: http://192.168.200.101:16010"
echo "  RegionServer UI: http://192.168.200.101:16030"
echo ""
echo "命令行工具:"
echo "  HBase Shell: /export/server/hbase/hbase/bin/hbase shell"
echo "  状态检查: /export/server/hbase/hbase/bin/hbase hbck"
echo ""
echo "日志文件:"
echo "  启动日志: /export/logs/hbase/hbase-root-master-*.out"
echo "  主日志: /export/logs/hbase/hbase.log"
echo "  安全日志: /export/logs/hbase/hbase-security.log"
echo ""
echo "常用命令:"
echo "  查看进程: ps aux | grep HMaster"
echo "  查看端口: netstat -tlnp | grep -E '16000|16010'"
echo "  查看日志: tail -f /export/logs/hbase/hbase.log"
