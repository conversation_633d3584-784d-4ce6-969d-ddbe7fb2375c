# 中间件集成部署指南

## 集成架构设计

### 1. 整体数据流

```mermaid
graph LR
    A[应用程序] --> B[Kafka Producer]
    B --> C[Kafka Cluster]
    C --> D[Kafka Consumer]
    D --> E[Elasticsearch]
    E --> F[Kibana Dashboard]
    
    G[ZooKeeper] --> C
    H[Monitoring] --> C
    H --> E
    H --> G
```

### 2. 组件依赖关系

```
启动顺序:
1. <PERSON><PERSON>eeper集群 (如果使用传统模式)
2. Kafka集群 (KRaft模式可独立启动)
3. Elasticsearch集群
4. 应用程序
5. 监控系统

依赖关系:
- Kafka → ZooKeeper (传统模式)
- 应用 → Kafka
- 数据分析 → Elasticsearch
- 监控 → 所有组件
```

## 数据管道设计

### 1. 实时数据管道

#### **日志收集管道**
```yaml
# Filebeat配置
filebeat.inputs:
- type: log
  paths:
    - /var/log/app/*.log
  fields:
    service: web-app
    environment: production

output.kafka:
  hosts: ["***************:9092", "***************:9092", "***************:9092"]
  topic: "logs-%{[fields.service]}"
  partition.round_robin:
    reachable_only: false
  required_acks: 1
  compression: snappy
  max_message_bytes: 1000000
```

#### **Kafka到Elasticsearch管道**
```json
// Logstash配置
input {
  kafka {
    bootstrap_servers => "***************:9092,***************:9092,***************:9092"
    topics => ["logs-web-app", "logs-api-service"]
    group_id => "logstash-group"
    consumer_threads => 3
  }
}

filter {
  if [fields][service] == "web-app" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:msg}" }
    }
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["***************:9200", "***************:9200", "***************:9200"]
    index => "logs-%{[fields][service]}-%{+YYYY.MM.dd}"
    template_name => "logs"
    template_pattern => "logs-*"
  }
}
```

### 2. 批量数据管道

#### **数据同步策略**
```python
# Python示例：批量数据同步
from kafka import KafkaProducer
from elasticsearch import Elasticsearch
import json
import time

class DataPipeline:
    def __init__(self):
        self.kafka_producer = KafkaProducer(
            bootstrap_servers=['***************:9092'],
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            batch_size=16384,
            linger_ms=10,
            compression_type='snappy'
        )
        
        self.es_client = Elasticsearch([
            {'host': '***************', 'port': 9200},
            {'host': '***************', 'port': 9200},
            {'host': '***************', 'port': 9200}
        ])
    
    def process_batch(self, data_batch):
        # 发送到Kafka进行实时处理
        for record in data_batch:
            self.kafka_producer.send('data-stream', record)
        
        # 批量索引到Elasticsearch
        actions = []
        for record in data_batch:
            action = {
                "_index": f"data-{record['type']}-{time.strftime('%Y.%m.%d')}",
                "_source": record
            }
            actions.append(action)
        
        from elasticsearch.helpers import bulk
        bulk(self.es_client, actions)
```

## 配置集成

### 1. 统一配置管理

#### **配置文件结构**
```
config/
├── common/
│   ├── cluster.properties      # 集群通用配置
│   ├── security.properties     # 安全配置
│   └── monitoring.properties   # 监控配置
├── zookeeper/
│   ├── zoo.cfg
│   └── jaas.conf
├── kafka/
│   ├── server.properties
│   └── client.properties
└── elasticsearch/
    ├── elasticsearch.yml
    └── jvm.options
```

#### **环境变量管理**
```bash
# 环境配置文件
cat > /etc/middleware/env.conf << 'EOF'
# 集群配置
CLUSTER_NAME=bigdata-cluster
CLUSTER_NODES="***************,***************,***************"

# Java环境
JAVA_HOME=/export/tools/jdk17
JAVA_OPTS="-Xms2g -Xmx2g -XX:+UseG1GC"

# 存储路径
DATA_DIR=/export/data
LOG_DIR=/export/logs
BACKUP_DIR=/export/backup

# 网络配置
NETWORK_INTERFACE=eth0
SECURITY_ENABLED=true
EOF
```

### 2. 安全集成

#### **统一认证配置**
```yaml
# 共享的SASL配置
sasl_config: &sasl_config
  security.protocol: SASL_PLAINTEXT
  sasl.mechanism: PLAIN
  sasl.jaas.config: |
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="admin"
    password="admin123";

# Kafka客户端配置
kafka_producer:
  <<: *sasl_config
  bootstrap.servers: "***************:9092,***************:9092,***************:9092"
  acks: all
  retries: 3

kafka_consumer:
  <<: *sasl_config
  bootstrap.servers: "***************:9092,***************:9092,***************:9092"
  group.id: "integration-group"
  auto.offset.reset: earliest
```

#### **SSL证书管理**
```bash
# 统一证书生成脚本
#!/bin/bash
create_certificates() {
    local service=$1
    local keystore_path="/export/certs/${service}"
    
    mkdir -p $keystore_path
    
    # 生成CA证书
    openssl req -new -x509 -keyout ${keystore_path}/ca-key -out ${keystore_path}/ca-cert -days 365 -nodes \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=Company/OU=IT/CN=ca"
    
    # 生成服务证书
    keytool -keystore ${keystore_path}/server.keystore.jks -alias ${service} -validity 365 -genkey -keyalg RSA \
        -dname "CN=${service}, OU=IT, O=Company, L=Beijing, ST=Beijing, C=CN" \
        -storepass changeit -keypass changeit
    
    # 签名证书
    keytool -keystore ${keystore_path}/server.keystore.jks -alias ${service} -certreq -file ${keystore_path}/cert-file \
        -storepass changeit
    
    openssl x509 -req -CA ${keystore_path}/ca-cert -CAkey ${keystore_path}/ca-key -in ${keystore_path}/cert-file \
        -out ${keystore_path}/cert-signed -days 365 -CAcreateserial
    
    # 导入证书
    keytool -keystore ${keystore_path}/server.keystore.jks -alias CARoot -import -file ${keystore_path}/ca-cert \
        -storepass changeit -noprompt
    
    keytool -keystore ${keystore_path}/server.keystore.jks -alias ${service} -import -file ${keystore_path}/cert-signed \
        -storepass changeit -noprompt
}

# 为每个服务生成证书
create_certificates "kafka"
create_certificates "elasticsearch"
create_certificates "zookeeper"
```

## 监控集成

### 1. 统一监控架构

#### **Prometheus配置**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "kafka_rules.yml"
  - "elasticsearch_rules.yml"
  - "zookeeper_rules.yml"

scrape_configs:
  # Kafka监控
  - job_name: 'kafka'
    static_configs:
      - targets: ['***************:9308', '***************:9308', '***************:9308']
    scrape_interval: 30s
    metrics_path: /metrics

  # Elasticsearch监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['***************:9114', '***************:9114', '***************:9114']
    scrape_interval: 30s

  # ZooKeeper监控
  - job_name: 'zookeeper'
    static_configs:
      - targets: ['***************:9141', '***************:9141', '***************:9141']
    scrape_interval: 30s

  # 系统监控
  - job_name: 'node'
    static_configs:
      - targets: ['***************:9100', '***************:9100', '***************:9100']
```

#### **告警规则**
```yaml
# kafka_rules.yml
groups:
  - name: kafka.rules
    rules:
      - alert: KafkaDown
        expr: up{job="kafka"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Kafka instance is down"
          description: "Kafka instance {{ $labels.instance }} has been down for more than 1 minute."

      - alert: KafkaHighLatency
        expr: kafka_network_requestmetrics_totaltimems{quantile="0.99"} > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Kafka high latency"
          description: "Kafka 99th percentile latency is {{ $value }}ms on {{ $labels.instance }}"

      - alert: KafkaConsumerLag
        expr: kafka_consumer_lag_sum > 10000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Kafka consumer lag is high"
          description: "Consumer lag is {{ $value }} on {{ $labels.instance }}"
```

### 2. 日志聚合

#### **统一日志格式**
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "level": "INFO",
  "service": "kafka",
  "node": "***************",
  "component": "broker",
  "message": "Started Kafka server",
  "metadata": {
    "broker_id": 1,
    "cluster_id": "kafka-cluster-001"
  }
}
```

#### **Elasticsearch索引模板**
```json
{
  "index_patterns": ["middleware-logs-*"],
  "template": {
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.lifecycle.name": "middleware-logs-policy",
      "index.refresh_interval": "30s"
    },
    "mappings": {
      "properties": {
        "timestamp": {"type": "date"},
        "level": {"type": "keyword"},
        "service": {"type": "keyword"},
        "node": {"type": "keyword"},
        "component": {"type": "keyword"},
        "message": {"type": "text"},
        "metadata": {"type": "object"}
      }
    }
  }
}
```

## 部署自动化

### 1. Docker Compose部署

#### **完整的docker-compose.yml**
```yaml
version: '3.8'

services:
  zookeeper-1:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper-1
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_SERVERS: zookeeper-1:2888:3888;zookeeper-2:2888:3888;zookeeper-3:2888:3888
    volumes:
      - zk1-data:/var/lib/zookeeper/data
      - zk1-logs:/var/lib/zookeeper/log

  kafka-1:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka-1
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
    volumes:
      - kafka1-data:/var/lib/kafka/data
    depends_on:
      - zookeeper-1

  elasticsearch-1:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    hostname: elasticsearch-1
    ports:
      - "9200:9200"
    environment:
      - node.name=elasticsearch-1
      - cluster.name=es-cluster
      - discovery.seed_hosts=elasticsearch-2,elasticsearch-3
      - cluster.initial_master_nodes=elasticsearch-1,elasticsearch-2,elasticsearch-3
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es1-data:/usr/share/elasticsearch/data

volumes:
  zk1-data:
  zk1-logs:
  kafka1-data:
  es1-data:
```

### 2. Ansible自动化部署

#### **Playbook结构**
```yaml
# site.yml
---
- hosts: zookeeper
  roles:
    - common
    - java
    - zookeeper

- hosts: kafka
  roles:
    - common
    - java
    - kafka

- hosts: elasticsearch
  roles:
    - common
    - java
    - elasticsearch

- hosts: monitoring
  roles:
    - common
    - prometheus
    - grafana
```

#### **变量配置**
```yaml
# group_vars/all.yml
cluster_name: bigdata-cluster
java_version: 17
data_dir: /export/data
log_dir: /export/logs

# 网络配置
network:
  zookeeper_port: 2181
  kafka_port: 9092
  elasticsearch_port: 9200

# 安全配置
security:
  enabled: true
  ssl_enabled: false
  sasl_mechanism: PLAIN

# 性能配置
performance:
  jvm_heap_size: 2g
  gc_algorithm: G1GC
```

## 故障恢复

### 1. 数据备份策略

#### **自动备份脚本**
```bash
#!/bin/bash
# 统一备份脚本

BACKUP_DIR="/export/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# ZooKeeper备份
echo "Backing up ZooKeeper..."
tar -czf $BACKUP_DIR/zookeeper-data.tar.gz /export/data/zookeeper/

# Kafka备份
echo "Backing up Kafka..."
tar -czf $BACKUP_DIR/kafka-data.tar.gz /export/data/kafka/

# Elasticsearch备份
echo "Backing up Elasticsearch..."
curl -X PUT "localhost:9200/_snapshot/backup_repo/snapshot_$(date +%Y%m%d_%H%M%S)?wait_for_completion=true"

# 配置文件备份
echo "Backing up configurations..."
tar -czf $BACKUP_DIR/configs.tar.gz /export/server/*/config/

# 清理旧备份
find /export/backup -type d -mtime +7 -exec rm -rf {} \;

echo "Backup completed: $BACKUP_DIR"
```

### 2. 灾难恢复流程

#### **恢复步骤**
```bash
# 1. 停止所有服务
systemctl stop kafka
systemctl stop zookeeper
systemctl stop elasticsearch

# 2. 恢复数据
tar -xzf backup/zookeeper-data.tar.gz -C /
tar -xzf backup/kafka-data.tar.gz -C /
curl -X POST "localhost:9200/_snapshot/backup_repo/snapshot_20240101/_restore"

# 3. 恢复配置
tar -xzf backup/configs.tar.gz -C /

# 4. 按顺序启动服务
systemctl start zookeeper
sleep 30
systemctl start kafka
sleep 30
systemctl start elasticsearch

# 5. 验证服务状态
./scripts/health-check.sh
```

## 性能调优

### 1. 端到端延迟优化

#### **延迟测试**
```bash
# Kafka端到端延迟测试
kafka-run-class.sh kafka.tools.EndToEndLatency \
  ***************:9092 test-topic 10000 1 1024

# Elasticsearch查询延迟测试
curl -X GET "localhost:9200/test-index/_search" \
  -H "Content-Type: application/json" \
  -d '{"query": {"match_all": {}}}' \
  -w "Total time: %{time_total}s\n"
```

#### **优化配置**
```properties
# Kafka低延迟配置
linger.ms=0
batch.size=1
acks=1
compression.type=none

# Elasticsearch低延迟配置
index.refresh_interval=1s
index.translog.flush_threshold_size=512mb
```

### 2. 吞吐量优化

#### **批量处理配置**
```properties
# Kafka高吞吐量配置
batch.size=65536
linger.ms=100
compression.type=snappy
buffer.memory=67108864

# Elasticsearch批量索引配置
index.refresh_interval=30s
index.number_of_replicas=0  # 索引时临时设置
```

## 总结

中间件集成的关键要点：

1. **统一架构**: 标准化的部署和配置模式
2. **数据流设计**: 高效的数据管道和处理流程
3. **安全集成**: 统一的认证和授权机制
4. **监控体系**: 全面的监控和告警系统
5. **自动化运维**: 自动化的部署、备份和恢复流程

通过合理的集成设计，可以构建一个高可用、高性能、易维护的大数据平台。
