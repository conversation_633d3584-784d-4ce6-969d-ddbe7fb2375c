<?xml version="1.0"?>
<configuration>
    <!-- ZooKeeper配置 -->
    <property>
        <name>hbase.zookeeper.quorum</name>
        <value>***************,192.168.200.102,192.168.200.103</value>
    </property>
    
    <property>
        <name>hbase.zookeeper.property.clientPort</name>
        <value>4181</value>
    </property>
    
    <!-- ZooKeeper SASL认证配置 -->
    <property>
        <name>hbase.zookeeper.property.authProvider.1</name>
        <value>org.apache.zookeeper.server.auth.SASLAuthenticationProvider</value>
    </property>
    
    <property>
        <name>hbase.zookeeper.property.jaasLoginRenew</name>
        <value>3600000</value>
    </property>

    <!-- 使用外置ZooKeeper配置 -->
    <property>
        <name>hbase.cluster.distributed</name>
        <value>true</value>
        <description>启用分布式模式以使用外置ZooKeeper</description>
    </property>



    <!-- HBase安全配置 -->
    <property>
        <name>hbase.security.authentication</name>
        <value>simple</value>
    </property>
    
    <property>
        <name>hbase.security.authorization</name>
        <value>false</value>
    </property>
    
    <!-- 数据存储配置 -->
    <property>
        <name>hbase.rootdir</name>
        <value>file:///export/data/hbase</value>
        <description>HBase数据存储目录（本地文件系统）</description>
    </property>

    <!-- 集群配置（注释） -->
    <!--
    <property>
        <name>hbase.cluster.distributed</name>
        <value>true</value>
    </property>

    <property>
        <name>hbase.rootdir</name>
        <value>hdfs://***************:9000/hbase</value>
    </property>

    <property>
        <name>hbase.master</name>
        <value>***************:16000</value>
    </property>
    -->
    
    <property>
        <name>hbase.master.info.port</name>
        <value>16010</value>
    </property>
    
    <property>
        <name>hbase.regionserver.info.port</name>
        <value>16030</value>
    </property>
    
    <!-- 数据目录配置 -->
    <property>
        <name>hbase.tmp.dir</name>
        <value>/export/data/hbase/tmp</value>
    </property>
    
    <!-- 性能优化配置 -->
    <property>
        <name>hbase.regionserver.handler.count</name>
        <value>30</value>
    </property>
    
    <property>
        <name>hbase.hregion.max.filesize</name>
        <value>10737418240</value>
    </property>
    
    <property>
        <name>hbase.hregion.memstore.flush.size</name>
        <value>134217728</value>
    </property>
</configuration>
