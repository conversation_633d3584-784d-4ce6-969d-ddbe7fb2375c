<?xml version="1.0"?>
<configuration>
    <!-- ZooKeeper配置 -->
    <property>
        <name>hbase.zookeeper.quorum</name>
        <value>***************,192.168.200.102,192.168.200.103</value>
    </property>
    
    <property>
        <name>hbase.zookeeper.property.clientPort</name>
        <value>4181</value>
    </property>
    
    <!-- ZooKeeper SASL认证配置 -->
    <property>
        <name>hbase.zookeeper.property.authProvider.1</name>
        <value>org.apache.zookeeper.server.auth.SASLAuthenticationProvider</value>
    </property>
    
    <property>
        <name>hbase.zookeeper.property.jaasLoginRenew</name>
        <value>3600000</value>
    </property>
    
    <!-- HBase安全配置 -->
    <property>
        <name>hbase.security.authentication</name>
        <value>simple</value>
        <description>HBase内部使用简单认证</description>
    </property>

    <property>
        <name>hbase.security.authorization</name>
        <value>false</value>
        <description>暂时禁用授权检查</description>
    </property>

    <!-- ZooKeeper SASL认证强制启用 -->
    <property>
        <name>zookeeper.sasl.client</name>
        <value>true</value>
        <description>强制启用ZooKeeper SASL客户端</description>
    </property>

    <property>
        <name>zookeeper.sasl.clientconfig</name>
        <value>Client</value>
        <description>ZooKeeper SASL客户端配置名称</description>
    </property>
    
    <!-- 单机模式配置 -->
    <property>
        <name>hbase.cluster.distributed</name>
        <value>false</value>
    </property>

    <property>
        <name>hbase.rootdir</name>
        <value>file:///export/data/hbase</value>
    </property>

    <!-- 集群配置（注释） -->
    <!--
    <property>
        <name>hbase.cluster.distributed</name>
        <value>true</value>
    </property>

    <property>
        <name>hbase.rootdir</name>
        <value>hdfs://***************:9000/hbase</value>
    </property>

    <property>
        <name>hbase.master</name>
        <value>***************:16000</value>
    </property>
    -->
    
    <property>
        <name>hbase.master.info.port</name>
        <value>16010</value>
    </property>
    
    <property>
        <name>hbase.regionserver.info.port</name>
        <value>16030</value>
    </property>
    
    <!-- 数据目录配置 -->
    <property>
        <name>hbase.tmp.dir</name>
        <value>/export/data/hbase/tmp</value>
    </property>
    
    <!-- 性能优化配置 -->
    <property>
        <name>hbase.regionserver.handler.count</name>
        <value>30</value>
    </property>
    
    <property>
        <name>hbase.hregion.max.filesize</name>
        <value>10737418240</value>
    </property>
    
    <property>
        <name>hbase.hregion.memstore.flush.size</name>
        <value>134217728</value>
    </property>
</configuration>
