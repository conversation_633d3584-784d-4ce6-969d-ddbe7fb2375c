# Kafka Log4j2配置文件 - YAML格式
# 适配ZooKeeper SASL认证环境

Configuration:
  status: WARN
  monitorInterval: 30

  Properties:
    Property:
      - name: "kafka.logs.dir"
        value: "/export/logs/kafka"

  Appenders:
    # 控制台输出
    Console:
      name: "STDOUT"
      target: "SYSTEM_OUT"
      PatternLayout:
        pattern: "[%d] %p %m (%c)%n"

    # Kafka服务器日志
    RollingFile:
      - name: "kafkaAppender"
        fileName: "${kafka.logs.dir}/server.log"
        filePattern: "${kafka.logs.dir}/server.log.%i"
        PatternLayout:
          pattern: "[%d] %p %m (%c)%n"
        Policies:
          SizeBasedTriggeringPolicy:
            size: "100MB"
        DefaultRolloverStrategy:
          max: "10"

      # 状态变更日志
      - name: "stateChangeAppender"
        fileName: "${kafka.logs.dir}/state-change.log"
        filePattern: "${kafka.logs.dir}/state-change.log.%i"
        PatternLayout:
          pattern: "[%d] %p %m (%c)%n"
        Policies:
          SizeBasedTriggeringPolicy:
            size: "100MB"
        DefaultRolloverStrategy:
          max: "10"

      # 请求日志
      - name: "requestAppender"
        fileName: "${kafka.logs.dir}/kafka-request.log"
        filePattern: "${kafka.logs.dir}/kafka-request.log.%i"
        PatternLayout:
          pattern: "[%d] %p %m (%c)%n"
        Policies:
          SizeBasedTriggeringPolicy:
            size: "100MB"
        DefaultRolloverStrategy:
          max: "10"

      # 清理日志
      - name: "cleanerAppender"
        fileName: "${kafka.logs.dir}/log-cleaner.log"
        filePattern: "${kafka.logs.dir}/log-cleaner.log.%i"
        PatternLayout:
          pattern: "[%d] %p %m (%c)%n"
        Policies:
          SizeBasedTriggeringPolicy:
            size: "100MB"
        DefaultRolloverStrategy:
          max: "10"

      # 控制器日志
      - name: "controllerAppender"
        fileName: "${kafka.logs.dir}/controller.log"
        filePattern: "${kafka.logs.dir}/controller.log.%i"
        PatternLayout:
          pattern: "[%d] %p %m (%c)%n"
        Policies:
          SizeBasedTriggeringPolicy:
            size: "100MB"
        DefaultRolloverStrategy:
          max: "10"

      # 授权日志
      - name: "authorizerAppender"
        fileName: "${kafka.logs.dir}/kafka-authorizer.log"
        filePattern: "${kafka.logs.dir}/kafka-authorizer.log.%i"
        PatternLayout:
          pattern: "[%d] %p %m (%c)%n"
        Policies:
          SizeBasedTriggeringPolicy:
            size: "100MB"
        DefaultRolloverStrategy:
          max: "10"

  Loggers:
    # 根日志配置
    Root:
      level: "INFO"
      AppenderRef:
        - ref: "STDOUT"
        - ref: "kafkaAppender"

    # Kafka特定日志配置
    Logger:
      - name: "kafka"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      - name: "kafka.network.RequestChannel$"
        level: "WARN"
        additivity: false
        AppenderRef:
          - ref: "requestAppender"

      - name: "kafka.request.logger"
        level: "WARN"
        additivity: false
        AppenderRef:
          - ref: "requestAppender"

      - name: "kafka.controller"
        level: "TRACE"
        additivity: false
        AppenderRef:
          - ref: "controllerAppender"

      - name: "kafka.log.LogCleaner"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "cleanerAppender"

      - name: "state.change.logger"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "stateChangeAppender"

      - name: "kafka.authorizer.logger"
        level: "WARN"
        additivity: false
        AppenderRef:
          - ref: "authorizerAppender"

      # ZooKeeper相关日志
      - name: "org.apache.zookeeper"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      - name: "org.I0Itec.zkclient"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      # SASL认证相关日志
      - name: "javax.security.sasl"
        level: "DEBUG"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      - name: "org.apache.kafka.common.security"
        level: "DEBUG"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      # Kafka客户端日志
      - name: "org.apache.kafka.clients"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      # Kafka服务器日志
      - name: "kafka.server"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"

      # Kafka集群日志
      - name: "kafka.cluster"
        level: "INFO"
        additivity: false
        AppenderRef:
          - ref: "kafkaAppender"
