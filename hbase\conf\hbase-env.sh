#!/bin/bash

# HBase环境配置文件 - 适配ZooKeeper SASL认证

# Java环境变量
export JAVA_HOME=/export/tools/jdk8

# HBase安装目录
export HBASE_HOME=/export/server/hbase

# HBase配置目录
export HBASE_CONF_DIR=/export/server/hbase/conf

# HBase日志目录
export HBASE_LOG_DIR=/export/logs/hbase

# HBase进程ID目录
export HBASE_PID_DIR=/export/server/hbase/pids

# HBase数据目录
export HBASE_DATA_DIR=/export/data/hbase

# ZooKeeper SASL认证配置
export HBASE_OPTS="$HBASE_OPTS -Djava.security.auth.login.config=$HBASE_CONF_DIR/jaas.conf"
export HBASE_OPTS="$HBASE_OPTS -Dzookeeper.sasl.client=true"
export HBASE_OPTS="$HBASE_OPTS -Dzookeeper.sasl.clientconfig=Client"
export HBASE_OPTS="$HBASE_OPTS -Djava.security.krb5.conf=/etc/krb5.conf"

# HBase Master JVM配置
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS -Xmx2g -Xms2g"
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS -Djava.security.auth.login.config=$HBASE_CONF_DIR/jaas.conf"

# HBase RegionServer JVM配置（单机模式 - 降低内存）
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS -Xmx1g -Xms1g"
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS -Djava.security.auth.login.config=$HBASE_CONF_DIR/jaas.conf"

# 集群模式JVM配置（注释）
# export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS -Xmx4g -Xms4g"

# HBase REST服务JVM配置
export HBASE_REST_OPTS="$HBASE_REST_OPTS -Xmx1g -Xms1g"
export HBASE_REST_OPTS="$HBASE_REST_OPTS -Djava.security.auth.login.config=$HBASE_CONF_DIR/jaas.conf"

# HBase Thrift服务JVM配置
export HBASE_THRIFT_OPTS="$HBASE_THRIFT_OPTS -Xmx1g -Xms1g"
export HBASE_THRIFT_OPTS="$HBASE_THRIFT_OPTS -Djava.security.auth.login.config=$HBASE_CONF_DIR/jaas.conf"

# 日志配置
export HBASE_ROOT_LOGGER=INFO,RFA
export HBASE_SECURITY_LOGGER=INFO,RFAS

# 创建必要的目录
mkdir -p $HBASE_LOG_DIR
mkdir -p $HBASE_PID_DIR
mkdir -p $HBASE_DATA_DIR

# 设置权限
chown -R appuser:appuser $HBASE_LOG_DIR
chown -R appuser:appuser $HBASE_PID_DIR
chown -R appuser:appuser $HBASE_DATA_DIR
chmod 755 $HBASE_LOG_DIR
chmod 755 $HBASE_PID_DIR
chmod 755 $HBASE_DATA_DIR
