#!/bin/bash

# Kafka 4.0停止脚本

echo "=== 停止Kafka 4.0 ==="

# 停止Kafka服务
echo "1. 停止Kafka服务..."
/export/server/kafka/bin/kafka-server-stop.sh

# 等待进程停止
echo "等待Kafka进程停止..."
sleep 10

# 检查进程是否完全停止
echo ""
echo "2. 检查进程状态..."
kafka_processes=$(ps aux | grep -E "kafka\.Kafka" | grep -v grep)
if [ -z "$kafka_processes" ]; then
    echo "✓ Kafka进程已停止"
else
    echo "⚠ 仍有Kafka进程运行:"
    echo "$kafka_processes"
    
    echo ""
    echo "强制终止残留进程..."
    pkill -f "kafka.Kafka"
    sleep 5
    
    remaining=$(ps aux | grep -E "kafka\.Kafka" | grep -v grep)
    if [ -z "$remaining" ]; then
        echo "✓ 所有Kafka进程已强制终止"
    else
        echo "✗ 仍有进程无法终止，请手动处理"
        echo "$remaining"
    fi
fi

# 检查端口释放
echo ""
echo "3. 检查端口释放..."
if netstat -tlnp | grep -q ":9092"; then
    echo "⚠ 端口9092仍被占用"
else
    echo "✓ 端口9092已释放"
fi

if netstat -tlnp | grep -q ":9093"; then
    echo "⚠ Controller端口9093仍被占用"
else
    echo "✓ Controller端口9093已释放"
fi

echo ""
echo "=== Kafka 4.0停止完成 ==="
