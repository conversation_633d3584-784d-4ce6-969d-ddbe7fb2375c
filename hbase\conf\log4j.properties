# HBase Log4j配置文件
# 适配ZooKeeper SASL认证环境

# 根日志配置
hbase.root.logger=INFO,console,RFA
hbase.security.logger=INFO,console,RFAS
hbase.log.dir=/export/logs/hbase
hbase.log.file=hbase.log

# 控制台输出配置
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.target=System.err
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{ISO8601} %-5p [%t] %c{2}: %m%n

# 滚动文件输出配置
log4j.appender.RFA=org.apache.log4j.RollingFileAppender
log4j.appender.RFA.File=${hbase.log.dir}/${hbase.log.file}
log4j.appender.RFA.MaxFileSize=100MB
log4j.appender.RFA.MaxBackupIndex=10
log4j.appender.RFA.layout=org.apache.log4j.PatternLayout
log4j.appender.RFA.layout.ConversionPattern=%d{ISO8601} %-5p [%t] %c{2}: %m%n

# 安全日志配置
log4j.appender.RFAS=org.apache.log4j.RollingFileAppender
log4j.appender.RFAS.File=${hbase.log.dir}/hbase-security.log
log4j.appender.RFAS.MaxFileSize=100MB
log4j.appender.RFAS.MaxBackupIndex=10
log4j.appender.RFAS.layout=org.apache.log4j.PatternLayout
log4j.appender.RFAS.layout.ConversionPattern=%d{ISO8601} %-5p [%t] %c{2}: %m%n

# 特定包的日志级别配置
log4j.logger.org.apache.hadoop.hbase=INFO
log4j.logger.org.apache.hadoop.hbase.master=INFO
log4j.logger.org.apache.hadoop.hbase.regionserver=INFO
log4j.logger.org.apache.hadoop.hbase.client=INFO

# ZooKeeper相关日志
log4j.logger.org.apache.zookeeper=INFO
log4j.logger.org.apache.hadoop.hbase.zookeeper=INFO

# SASL认证相关日志
log4j.logger.javax.security.sasl=DEBUG
log4j.logger.org.apache.hadoop.security=DEBUG

# 网络相关日志
log4j.logger.org.apache.hadoop.ipc=INFO
log4j.logger.org.apache.hadoop.hbase.ipc=INFO

# 性能相关日志
log4j.logger.org.apache.hadoop.hbase.util.FSUtils=WARN
log4j.logger.org.apache.hadoop.hbase.util.Bytes=WARN
log4j.logger.org.apache.hadoop.hbase.util.JVMClusterUtil=INFO

# 减少冗余日志
log4j.logger.org.apache.hadoop.hbase.backup=WARN
log4j.logger.org.apache.hadoop.hbase.procedure2=WARN
