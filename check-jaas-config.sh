#!/bin/bash

# JAAS配置文件检查脚本
# 验证所有组件的JAAS配置是否正确

echo "=== JAAS配置文件检查 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_file() {
    local file=$1
    local component=$2
    
    echo ""
    echo "检查 $component JAAS配置..."
    
    if [ ! -f "$file" ]; then
        echo -e "  ${RED}✗${NC} 文件不存在: $file"
        return 1
    fi
    
    echo -e "  ${GREEN}✓${NC} 文件存在: $file"
    
    # 检查文件权限
    if [ -r "$file" ]; then
        echo -e "  ${GREEN}✓${NC} 文件可读"
    else
        echo -e "  ${RED}✗${NC} 文件不可读"
        return 1
    fi
    
    # 检查文件内容
    if [ -s "$file" ]; then
        echo -e "  ${GREEN}✓${NC} 文件非空"
    else
        echo -e "  ${RED}✗${NC} 文件为空"
        return 1
    fi
    
    return 0
}

# 检查密码匹配
check_password_match() {
    echo ""
    echo "检查密码匹配..."
    
    # 从ZooKeeper配置中提取HBase密码
    if [ -f "zookeeper/jaas.conf" ]; then
        zk_hbase_pass=$(grep 'user_hbase=' zookeeper/jaas.conf | cut -d'"' -f2)
        echo "  ZooKeeper中HBase密码: $zk_hbase_pass"
    else
        echo -e "  ${RED}✗${NC} ZooKeeper JAAS配置文件不存在"
        return 1
    fi
    
    # 从HBase配置中提取密码
    if [ -f "hbase/conf/jaas.conf" ]; then
        hbase_pass=$(grep 'password=' hbase/conf/jaas.conf | head -1 | cut -d'"' -f2)
        echo "  HBase中配置密码: $hbase_pass"
        
        if [ "$zk_hbase_pass" = "$hbase_pass" ]; then
            echo -e "  ${GREEN}✓${NC} HBase密码匹配"
        else
            echo -e "  ${RED}✗${NC} HBase密码不匹配"
            return 1
        fi
    else
        echo -e "  ${RED}✗${NC} HBase JAAS配置文件不存在"
        return 1
    fi
    
    return 0
}

# 检查配置语法
check_syntax() {
    local file=$1
    local component=$2
    
    echo ""
    echo "检查 $component 配置语法..."
    
    # 检查基本语法结构
    if grep -q "LoginModule required" "$file"; then
        echo -e "  ${GREEN}✓${NC} LoginModule配置正确"
    else
        echo -e "  ${RED}✗${NC} LoginModule配置缺失或错误"
        return 1
    fi
    
    # 检查是否有用户名和密码配置
    if grep -q "username=" "$file" || grep -q "user_" "$file"; then
        echo -e "  ${GREEN}✓${NC} 用户配置存在"
    else
        echo -e "  ${RED}✗${NC} 用户配置缺失"
        return 1
    fi
    
    if grep -q "password=" "$file"; then
        echo -e "  ${GREEN}✓${NC} 密码配置存在"
    else
        echo -e "  ${RED}✗${NC} 密码配置缺失"
        return 1
    fi
    
    return 0
}

# 主检查流程
main() {
    local error_count=0
    
    # 检查ZooKeeper JAAS配置
    if check_file "zookeeper/jaas.conf" "ZooKeeper"; then
        check_syntax "zookeeper/jaas.conf" "ZooKeeper" || ((error_count++))
    else
        ((error_count++))
    fi
    
    # 检查HBase JAAS配置
    if check_file "hbase/conf/jaas.conf" "HBase"; then
        check_syntax "hbase/conf/jaas.conf" "HBase" || ((error_count++))
    else
        ((error_count++))
    fi
    
    # 检查Kafka JAAS配置（可选）
    if [ -f "kafka/conf/jaas.conf" ]; then
        if check_file "kafka/conf/jaas.conf" "Kafka"; then
            check_syntax "kafka/conf/jaas.conf" "Kafka" || ((error_count++))
        else
            ((error_count++))
        fi
    else
        echo ""
        echo -e "  ${YELLOW}⚠${NC} Kafka JAAS配置文件不存在（KRaft模式下可选）"
    fi
    
    # 检查密码匹配
    check_password_match || ((error_count++))
    
    # 总结
    echo ""
    echo "=== 检查结果 ==="
    if [ $error_count -eq 0 ]; then
        echo -e "${GREEN}✓ 所有JAAS配置检查通过${NC}"
        echo ""
        echo "建议："
        echo "1. 定期更换密码"
        echo "2. 确保配置文件权限安全（600或640）"
        echo "3. 在生产环境中使用更复杂的密码"
    else
        echo -e "${RED}✗ 发现 $error_count 个问题${NC}"
        echo ""
        echo "请修复上述问题后重新运行检查"
        exit 1
    fi
}

# 运行主函数
main
