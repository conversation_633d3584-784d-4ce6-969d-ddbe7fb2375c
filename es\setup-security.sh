#!/bin/bash

# Elasticsearch安全配置脚本

echo "=== Elasticsearch安全配置 ==="

# 检查Elasticsearch安装
if [ ! -d "/export/server/elasticsearch" ]; then
    echo "✗ 错误: Elasticsearch未安装在 /export/server/elasticsearch"
    exit 1
fi

cd /export/server/elasticsearch

# 设置Java环境
export JAVA_HOME=/export/tools/jdk17
export PATH=$JAVA_HOME/bin:$PATH

echo "✓ Elasticsearch目录: /export/server/elasticsearch"

# 创建证书目录
echo ""
echo "1. 创建证书目录..."
mkdir -p config/certs
echo "✓ 证书目录已创建"

# 生成CA证书
echo ""
echo "2. 生成CA证书..."
if [ ! -f "config/certs/elastic-stack-ca.p12" ]; then
    bin/elasticsearch-certutil ca --out config/certs/elastic-stack-ca.p12 --pass ""
    echo "✓ CA证书已生成"
else
    echo "✓ CA证书已存在"
fi

# 生成节点证书
echo ""
echo "3. 生成节点证书..."
if [ ! -f "config/certs/elastic-certificates.p12" ]; then
    bin/elasticsearch-certutil cert --ca config/certs/elastic-stack-ca.p12 --ca-pass "" --out config/certs/elastic-certificates.p12 --pass ""
    echo "✓ 节点证书已生成"
else
    echo "✓ 节点证书已存在"
fi

# 设置证书权限
echo ""
echo "4. 设置证书权限..."
chmod 660 config/certs/*.p12
echo "✓ 证书权限已设置"

# 复制证书到其他节点（如果有SSH访问权限）
echo ""
echo "5. 复制证书到其他节点..."
nodes=("192.168.200.102" "192.168.200.103")

for node in "${nodes[@]}"; do
    echo "复制证书到节点 $node..."
    if ssh -o ConnectTimeout=5 root@$node "mkdir -p /export/server/elasticsearch/config/certs" 2>/dev/null; then
        scp config/certs/*.p12 root@$node:/export/server/elasticsearch/config/certs/ 2>/dev/null
        ssh root@$node "chmod 660 /export/server/elasticsearch/config/certs/*.p12" 2>/dev/null
        echo "  ✓ 证书已复制到 $node"
    else
        echo "  ⚠ 无法SSH到 $node，请手动复制证书"
        echo "    手动复制命令:"
        echo "    scp config/certs/*.p12 root@$node:/export/server/elasticsearch/config/certs/"
    fi
done

# 生成内置用户密码
echo ""
echo "6. 设置内置用户密码..."
echo "正在生成随机密码..."

# 创建密码文件
cat > /tmp/es-passwords.txt << EOF
# Elasticsearch内置用户密码
# 生成时间: $(date)

elastic: $(openssl rand -base64 12)
kibana_system: $(openssl rand -base64 12)
logstash_system: $(openssl rand -base64 12)
beats_system: $(openssl rand -base64 12)
apm_system: $(openssl rand -base64 12)
remote_monitoring_user: $(openssl rand -base64 12)
EOF

echo "✓ 密码文件已生成: /tmp/es-passwords.txt"
echo ""
echo "密码信息:"
cat /tmp/es-passwords.txt

# 提供手动设置密码的命令
echo ""
echo "7. 设置密码说明..."
echo "启动Elasticsearch后，请运行以下命令设置密码:"
echo ""
echo "# 自动生成密码"
echo "bin/elasticsearch-setup-passwords auto"
echo ""
echo "# 或手动设置密码"
echo "bin/elasticsearch-setup-passwords interactive"
echo ""
echo "# 或使用API设置密码"
echo "curl -X POST \"localhost:9200/_security/user/elastic/_password\" -H \"Content-Type: application/json\" -d '{\"password\":\"your-password\"}'"

# 创建安全配置检查脚本
echo ""
echo "8. 创建安全检查脚本..."
cat > check-security.sh << 'EOF'
#!/bin/bash

echo "=== Elasticsearch安全状态检查 ==="

# 检查证书文件
echo "1. 证书文件检查:"
if [ -f "config/certs/elastic-certificates.p12" ]; then
    echo "  ✓ 节点证书存在"
else
    echo "  ✗ 节点证书缺失"
fi

if [ -f "config/certs/elastic-stack-ca.p12" ]; then
    echo "  ✓ CA证书存在"
else
    echo "  ✗ CA证书缺失"
fi

# 检查配置文件
echo ""
echo "2. 安全配置检查:"
if grep -q "xpack.security.enabled: true" config/elasticsearch.yml; then
    echo "  ✓ 安全功能已启用"
else
    echo "  ⚠ 安全功能未启用"
fi

if grep -q "xpack.security.transport.ssl.enabled: true" config/elasticsearch.yml; then
    echo "  ✓ 传输层SSL已启用"
else
    echo "  ⚠ 传输层SSL未启用"
fi

# 检查服务状态
echo ""
echo "3. 服务状态检查:"
if curl -s http://localhost:9200 > /dev/null 2>&1; then
    echo "  ✓ Elasticsearch服务可访问"
    
    # 检查安全状态
    if curl -s http://localhost:9200 | grep -q "missing authentication"; then
        echo "  ✓ 安全认证已启用"
    else
        echo "  ⚠ 安全认证可能未启用"
    fi
else
    echo "  ✗ Elasticsearch服务不可访问"
fi

echo ""
echo "=== 检查完成 ==="
EOF

chmod +x check-security.sh
echo "✓ 安全检查脚本已创建: check-security.sh"

echo ""
echo "=== 安全配置完成 ==="
echo ""
echo "下一步操作:"
echo "1. 启动Elasticsearch: ./start-elasticsearch.sh"
echo "2. 设置用户密码: bin/elasticsearch-setup-passwords auto"
echo "3. 检查安全状态: ./check-security.sh"
echo "4. 测试认证: curl -u elastic:password http://localhost:9200"
echo ""
echo "重要文件:"
echo "  证书目录: config/certs/"
echo "  密码文件: /tmp/es-passwords.txt"
echo "  安全检查: ./check-security.sh"
