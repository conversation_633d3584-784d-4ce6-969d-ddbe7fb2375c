<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Hadoop HDFS HA集群配置 -->
    
    <!-- NameService配置 -->
    <property>
        <name>dfs.nameservices</name>
        <value>hadoop-cluster</value>
        <description>HA集群的逻辑名称</description>
    </property>
    
    <!-- NameNode节点配置 -->
    <property>
        <name>dfs.ha.namenodes.hadoop-cluster</name>
        <value>nn1,nn2</value>
        <description>NameNode节点标识</description>
    </property>
    
    <!-- NameNode RPC地址配置 -->
    <property>
        <name>dfs.namenode.rpc-address.hadoop-cluster.nn1</name>
        <value>***************:9000</value>
        <description>NameNode1 RPC地址</description>
    </property>
    
    <property>
        <name>dfs.namenode.rpc-address.hadoop-cluster.nn2</name>
        <value>***************:9000</value>
        <description>NameNode2 RPC地址</description>
    </property>
    
    <!-- NameNode HTTP地址配置 -->
    <property>
        <name>dfs.namenode.http-address.hadoop-cluster.nn1</name>
        <value>***************:9870</value>
        <description>NameNode1 HTTP地址</description>
    </property>
    
    <property>
        <name>dfs.namenode.http-address.hadoop-cluster.nn2</name>
        <value>***************:9870</value>
        <description>NameNode2 HTTP地址</description>
    </property>
    
    <!-- JournalNode配置 -->
    <property>
        <name>dfs.namenode.shared.edits.dir</name>
        <value>qjournal://***************:8485;***************:8485;***************:8485/hadoop-cluster</value>
        <description>JournalNode集群地址</description>
    </property>
    
    <property>
        <name>dfs.journalnode.edits.dir</name>
        <value>/export/data/hadoop/journalnode</value>
        <description>JournalNode数据目录</description>
    </property>
    
    <!-- 故障转移配置 -->
    <property>
        <name>dfs.client.failover.proxy.provider.hadoop-cluster</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
        <description>故障转移代理提供者</description>
    </property>
    
    <!-- 自动故障转移配置 -->
    <property>
        <name>dfs.ha.automatic-failover.enabled</name>
        <value>true</value>
        <description>启用自动故障转移</description>
    </property>
    
    <!-- Fencing配置 -->
    <property>
        <name>dfs.ha.fencing.methods</name>
        <value>sshfence</value>
        <description>故障转移隔离方法</description>
    </property>
    
    <property>
        <name>dfs.ha.fencing.ssh.private-key-files</name>
        <value>/root/.ssh/id_rsa</value>
        <description>SSH私钥文件路径</description>
    </property>
    
    <!-- NameNode数据目录 -->
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/export/data/hadoop/namenode</value>
        <description>NameNode数据目录</description>
    </property>
    
    <!-- DataNode数据目录 -->
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/export/data/hadoop/datanode</value>
        <description>DataNode数据目录</description>
    </property>
    
    <!-- 副本数量 -->
    <property>
        <name>dfs.replication</name>
        <value>3</value>
        <description>数据副本数量</description>
    </property>
    
    <!-- 块大小 -->
    <property>
        <name>dfs.blocksize</name>
        <value>134217728</value>
        <description>HDFS块大小(128MB)</description>
    </property>
    
    <!-- DataNode处理线程数 -->
    <property>
        <name>dfs.datanode.handler.count</name>
        <value>10</value>
        <description>DataNode处理线程数</description>
    </property>
    
    <!-- NameNode处理线程数 -->
    <property>
        <name>dfs.namenode.handler.count</name>
        <value>20</value>
        <description>NameNode处理线程数</description>
    </property>
    
    <!-- 权限检查 -->
    <property>
        <name>dfs.permissions.enabled</name>
        <value>false</value>
        <description>禁用权限检查</description>
    </property>
    
    <!-- WebHDFS启用 -->
    <property>
        <name>dfs.webhdfs.enabled</name>
        <value>true</value>
        <description>启用WebHDFS</description>
    </property>

    <!-- 安全模式配置 -->
    <property>
        <name>dfs.namenode.safemode.threshold-pct</name>
        <value>0.9f</value>
        <description>安全模式阈值</description>
    </property>

    <!-- 心跳间隔 -->
    <property>
        <name>dfs.heartbeat.interval</name>
        <value>3</value>
        <description>心跳间隔(秒)</description>
    </property>

    <!-- DataNode超时时间 -->
    <property>
        <name>dfs.namenode.heartbeat.recheck-interval</name>
        <value>300000</value>
        <description>DataNode超时检查间隔(毫秒)</description>
    </property>
</configuration>
