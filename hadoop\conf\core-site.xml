<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Hadoop HA集群核心配置 -->
    
    <!-- 默认文件系统 - 使用NameService -->
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://hadoop-cluster</value>
        <description>HA集群的逻辑名称</description>
    </property>
    
    <!-- Hadoop临时目录 -->
    <property>
        <name>hadoop.tmp.dir</name>
        <value>/export/data/hadoop/tmp</value>
        <description>Hadoop临时文件目录</description>
    </property>
    
    <!-- ZooKeeper集群配置 -->
    <property>
        <name>ha.zookeeper.quorum</name>
        <value>***************:4181,***************:4181,***************:4181</value>
        <description>ZooKeeper集群地址</description>
    </property>
    
    <!-- ZooKeeper会话超时 -->
    <property>
        <name>ha.zookeeper.session-timeout.ms</name>
        <value>10000</value>
        <description>ZooKeeper会话超时时间</description>
    </property>
    
    <!-- 启用垃圾回收 -->
    <property>
        <name>fs.trash.interval</name>
        <value>1440</value>
        <description>垃圾回收间隔(分钟)</description>
    </property>
    
    <!-- IO缓冲区大小 -->
    <property>
        <name>io.file.buffer.size</name>
        <value>131072</value>
        <description>IO缓冲区大小</description>
    </property>
    
    <!-- 用户代理配置 -->
    <property>
        <name>hadoop.proxyuser.root.hosts</name>
        <value>*</value>
    </property>
    
    <property>
        <name>hadoop.proxyuser.root.groups</name>
        <value>*</value>
    </property>
    
    <!-- HTTP认证配置 -->
    <property>
        <name>hadoop.http.authentication.type</name>
        <value>simple</value>
    </property>
    
    <!-- 安全配置 -->
    <property>
        <name>hadoop.security.authentication</name>
        <value>simple</value>
    </property>
    
    <property>
        <name>hadoop.security.authorization</name>
        <value>false</value>
    </property>
</configuration>
