// HBase JAAS配置文件 - 适配ZooKeeper SASL认证
// 使用增强安全密码

// HBase Master进程的ZooKeeper客户端认证
Client {
    org.apache.zookeeper.server.auth.DigestLoginModule required
    username="hbase"
    password="HBase\$ecure2024";
};

// HBase RegionServer进程的ZooKeeper客户端认证
RegionServer {
    org.apache.zookeeper.server.auth.DigestLoginModule required
    username="hbase"
    password="HBase\$ecure2024";
};

// HBase REST服务的ZooKeeper客户端认证
REST {
    org.apache.zookeeper.server.auth.DigestLoginModule required
    username="hbase"
    password="HBase\$ecure2024";
};

// HBase Thrift服务的ZooKeeper客户端认证
Thrift {
    org.apache.zookeeper.server.auth.DigestLoginModule required
    username="hbase"
    password="HBase\$ecure2024";
};
