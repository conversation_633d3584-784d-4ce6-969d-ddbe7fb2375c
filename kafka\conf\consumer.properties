# Kafka 4.0消费者配置文件
# 包含SASL/PLAIN认证

# Bootstrap服务器列表
bootstrap.servers=192.168.200.101:9092

# SASL认证配置
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="consumer" password="consumer123";

# 消费者组ID
group.id=test-consumer-group

# 自动提交偏移量
enable.auto.commit=true

# 自动提交间隔
auto.commit.interval.ms=1000

# 会话超时时间
session.timeout.ms=30000

# 心跳间隔
heartbeat.interval.ms=3000

# 键反序列化器
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer

# 值反序列化器
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer

# 自动偏移量重置策略
auto.offset.reset=earliest

# 最大拉取记录数
max.poll.records=500

# 拉取最大等待时间
fetch.max.wait.ms=500

# 拉取最小字节数
fetch.min.bytes=1

# 拉取最大字节数
fetch.max.bytes=52428800

# 每个分区拉取最大字节数
max.partition.fetch.bytes=1048576

# 连接最大空闲时间
connections.max.idle.ms=540000

# 请求超时时间
request.timeout.ms=30000

# 重试次数
retries=2147483647

# 重试间隔
retry.backoff.ms=100

# 重连间隔
reconnect.backoff.ms=50

# 最大重连间隔
reconnect.backoff.max.ms=1000
