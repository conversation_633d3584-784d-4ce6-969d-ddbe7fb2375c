#!/bin/bash

# Hadoop环境配置文件
# 适用于Hadoop 3.x HA集群

# Java环境变量
export JAVA_HOME=/export/tools/jdk8

# Hadoop安装目录
export HADOOP_HOME=/export/server/hadoop
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop

# Hadoop日志目录
export HADOOP_LOG_DIR=/export/logs/hadoop

# Hadoop进程ID目录
export HADOOP_PID_DIR=/export/server/hadoop/pids

# Hadoop用户
export HADOOP_USER_NAME=root

# JVM配置
export HADOOP_HEAPSIZE_MAX=2048m

# NameNode JVM配置
export HDFS_NAMENODE_OPTS="-Xmx2g -Xms2g -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:$HADOOP_LOG_DIR/namenode-gc.log"

# DataNode JVM配置
export HDFS_DATANODE_OPTS="-Xmx1g -Xms1g -XX:+UseG1GC"

# SecondaryNameNode JVM配置
export HDFS_SECONDARYNAMENODE_OPTS="-Xmx1g -Xms1g -XX:+UseG1GC"

# JournalNode JVM配置
export HDFS_JOURNALNODE_OPTS="-Xmx1g -Xms1g -XX:+UseG1GC"

# ZKFC JVM配置
export HDFS_ZKFC_OPTS="-Xmx512m -Xms512m"

# ResourceManager JVM配置
export YARN_RESOURCEMANAGER_OPTS="-Xmx2g -Xms2g -XX:+UseG1GC"

# NodeManager JVM配置
export YARN_NODEMANAGER_OPTS="-Xmx1g -Xms1g -XX:+UseG1GC"

# TimelineService JVM配置
export YARN_TIMELINESERVICE_OPTS="-Xmx1g -Xms1g -XX:+UseG1GC"

# JobHistoryServer JVM配置
export MAPRED_HISTORYSERVER_OPTS="-Xmx1g -Xms1g -XX:+UseG1GC"

# 启用JMX监控
export HADOOP_JMX_BASE="-Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false"

# NameNode JMX端口
export HDFS_NAMENODE_OPTS="$HDFS_NAMENODE_OPTS $HADOOP_JMX_BASE -Dcom.sun.management.jmxremote.port=50070"

# DataNode JMX端口
export HDFS_DATANODE_OPTS="$HDFS_DATANODE_OPTS $HADOOP_JMX_BASE -Dcom.sun.management.jmxremote.port=50075"

# ResourceManager JMX端口
export YARN_RESOURCEMANAGER_OPTS="$YARN_RESOURCEMANAGER_OPTS $HADOOP_JMX_BASE -Dcom.sun.management.jmxremote.port=50088"

# NodeManager JMX端口
export YARN_NODEMANAGER_OPTS="$YARN_NODEMANAGER_OPTS $HADOOP_JMX_BASE -Dcom.sun.management.jmxremote.port=50060"

# 安全配置
export HADOOP_SECURE_DN_USER=root
export HADOOP_SECURE_DN_PID_DIR=$HADOOP_PID_DIR
export HADOOP_SECURE_DN_LOG_DIR=$HADOOP_LOG_DIR

# SSH配置
export HADOOP_SSH_OPTS="-o ConnectTimeout=10 -o SendEnv=HADOOP_CONF_DIR"

# 本地库路径
export HADOOP_COMMON_LIB_NATIVE_DIR=$HADOOP_HOME/lib/native
export HADOOP_OPTS="$HADOOP_OPTS -Djava.library.path=$HADOOP_HOME/lib/native"

# 启用本地库
export HADOOP_OPTS="$HADOOP_OPTS -Djava.net.preferIPv4Stack=true"

# 日志级别
export HADOOP_ROOT_LOGGER=INFO,RFA

# 审计日志
export HDFS_AUDIT_LOGGER=INFO,NullAppender

# 创建必要目录
mkdir -p $HADOOP_LOG_DIR
mkdir -p $HADOOP_PID_DIR
mkdir -p /export/data/hadoop/namenode
mkdir -p /export/data/hadoop/datanode
mkdir -p /export/data/hadoop/journalnode
mkdir -p /export/data/hadoop/tmp
mkdir -p /export/data/hadoop/yarn/local
mkdir -p /export/logs/hadoop/yarn
mkdir -p /export/data/hadoop/mr-history/tmp
mkdir -p /export/data/hadoop/mr-history/done
