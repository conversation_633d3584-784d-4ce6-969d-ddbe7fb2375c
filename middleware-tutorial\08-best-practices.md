# 最佳实践指南

## 架构设计最佳实践

### 1. 容量规划

#### **硬件选型原则**
```
CPU选择:
- 优先选择高频率CPU (3.0GHz+)
- 核心数量: 8-16核 (根据负载调整)
- 避免超线程对延迟敏感应用的影响

内存配置:
- 最小16GB，推荐32GB+
- 使用ECC内存提高可靠性
- 预留50%内存给操作系统页缓存

存储选择:
- 数据盘: NVMe SSD (高IOPS)
- 日志盘: SATA SSD (顺序写优化)
- 备份盘: HDD (成本考虑)

网络配置:
- 万兆网卡 (10Gbps+)
- 低延迟网络交换机
- 冗余网络连接
```

#### **容量计算公式**
```bash
# Kafka存储容量计算
daily_data_gb = 日均数据量(GB)
retention_days = 数据保留天数
replication_factor = 副本数
growth_factor = 1.5  # 增长预留

kafka_storage = daily_data_gb * retention_days * replication_factor * growth_factor

# Elasticsearch存储容量计算
index_size_gb = 索引大小(GB)
replica_count = 副本数
index_count = 索引数量
overhead_factor = 1.3  # 开销预留

es_storage = index_size_gb * (1 + replica_count) * index_count * overhead_factor

# 示例计算
echo "Kafka存储需求: $((100 * 7 * 3 * 1.5))GB = 3150GB"
echo "Elasticsearch存储需求: $((50 * 2 * 30 * 1.3))GB = 3900GB"
```

### 2. 网络架构设计

#### **网络分层设计**
```
接入层 (Access Layer):
- 客户端连接
- 负载均衡
- SSL终结

聚合层 (Aggregation Layer):
- 服务间通信
- 路由和过滤
- QoS控制

核心层 (Core Layer):
- 高速转发
- 冗余设计
- 最小延迟

管理网络:
- 独立的管理VLAN
- 带外管理
- 监控和运维
```

#### **网络优化配置**
```bash
# TCP参数优化
cat >> /etc/sysctl.conf << 'EOF'
# 网络缓冲区优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.rmem_default = 65536
net.core.wmem_default = 65536

# TCP窗口缩放
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# 连接数优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535

# 时间戳和选择性确认
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1

# 快速回收TIME_WAIT连接
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
EOF

sysctl -p
```

## 性能调优最佳实践

### 1. JVM调优

#### **通用JVM参数**
```bash
# 基础内存设置
JAVA_OPTS="-Xms4g -Xmx4g"

# 垃圾收集器选择
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:G1HeapRegionSize=16m"

# GC日志配置
JAVA_OPTS="$JAVA_OPTS -Xlog:gc*:gc.log:time,tags"
JAVA_OPTS="$JAVA_OPTS -XX:+UseGCLogFileRotation"
JAVA_OPTS="$JAVA_OPTS -XX:NumberOfGCLogFiles=10"
JAVA_OPTS="$JAVA_OPTS -XX:GCLogFileSize=100M"

# 内存溢出处理
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=/export/logs/heapdump/"

# JIT编译优化
JAVA_OPTS="$JAVA_OPTS -XX:+TieredCompilation"
JAVA_OPTS="$JAVA_OPTS -XX:TieredStopAtLevel=1"  # 快速启动

# 大页内存支持
JAVA_OPTS="$JAVA_OPTS -XX:+UseLargePages"
JAVA_OPTS="$JAVA_OPTS -XX:LargePageSizeInBytes=2m"
```

#### **服务特定调优**
```bash
# Kafka JVM调优
KAFKA_JVM_OPTS="-Xms2g -Xmx2g"
KAFKA_JVM_OPTS="$KAFKA_JVM_OPTS -XX:+UseG1GC"
KAFKA_JVM_OPTS="$KAFKA_JVM_OPTS -XX:MaxGCPauseMillis=20"
KAFKA_JVM_OPTS="$KAFKA_JVM_OPTS -XX:InitiatingHeapOccupancyPercent=35"
KAFKA_JVM_OPTS="$KAFKA_JVM_OPTS -XX:+ExplicitGCInvokesConcurrent"

# Elasticsearch JVM调优
ES_JVM_OPTS="-Xms4g -Xmx4g"
ES_JVM_OPTS="$ES_JVM_OPTS -XX:+UseG1GC"
ES_JVM_OPTS="$ES_JVM_OPTS -XX:G1ReservePercent=25"
ES_JVM_OPTS="$ES_JVM_OPTS -XX:InitiatingHeapOccupancyPercent=30"
ES_JVM_OPTS="$ES_JVM_OPTS -Djava.io.tmpdir=/tmp"

# ZooKeeper JVM调优
ZK_JVM_OPTS="-Xms1g -Xmx1g"
ZK_JVM_OPTS="$ZK_JVM_OPTS -XX:+UseG1GC"
ZK_JVM_OPTS="$ZK_JVM_OPTS -XX:MaxGCPauseMillis=50"
```

### 2. 操作系统调优

#### **文件系统优化**
```bash
# 文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf
echo "* soft nproc 32768" >> /etc/security/limits.conf
echo "* hard nproc 32768" >> /etc/security/limits.conf

# 内存锁定限制
echo "* soft memlock unlimited" >> /etc/security/limits.conf
echo "* hard memlock unlimited" >> /etc/security/limits.conf

# 虚拟内存设置
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'vm.swappiness=1' >> /etc/sysctl.conf
echo 'vm.dirty_ratio=15' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio=5' >> /etc/sysctl.conf

# 文件系统挂载优化
# 在/etc/fstab中添加
/dev/sdb1 /export/data ext4 defaults,noatime,nodiratime 0 2
```

#### **磁盘I/O优化**
```bash
# 设置I/O调度器
echo 'deadline' > /sys/block/sdb/queue/scheduler

# 调整读取预读
echo '128' > /sys/block/sdb/queue/read_ahead_kb

# 设置队列深度
echo '32' > /sys/block/sdb/queue/nr_requests

# 永久化设置
cat >> /etc/udev/rules.d/60-scheduler.rules << 'EOF'
ACTION=="add|change", KERNEL=="sd[a-z]", ATTR{queue/scheduler}="deadline"
ACTION=="add|change", KERNEL=="sd[a-z]", ATTR{queue/read_ahead_kb}="128"
ACTION=="add|change", KERNEL=="sd[a-z]", ATTR{queue/nr_requests}="32"
EOF
```

## 安全最佳实践

### 1. 网络安全

#### **防火墙配置**
```bash
# iptables规则示例
#!/bin/bash

# 清空现有规则
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# 设置默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 允许SSH (限制来源IP)
iptables -A INPUT -p tcp --dport 22 -s *************/24 -j ACCEPT

# 允许集群内通信
iptables -A INPUT -s *************/24 -j ACCEPT

# 允许特定服务端口 (限制来源)
iptables -A INPUT -p tcp --dport 9092 -s 10.0.0.0/8 -j ACCEPT  # Kafka
iptables -A INPUT -p tcp --dport 9200 -s 10.0.0.0/8 -j ACCEPT  # Elasticsearch
iptables -A INPUT -p tcp --dport 2181 -s *************/24 -j ACCEPT  # ZooKeeper

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

#### **SSL/TLS配置**
```bash
# 生成强密钥
openssl genrsa -out server.key 4096

# 生成证书请求
openssl req -new -key server.key -out server.csr \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=Company/OU=IT/CN=kafka.company.com"

# 生成自签名证书 (测试环境)
openssl x509 -req -days 365 -in server.csr -signkey server.key -out server.crt

# 生成Java密钥库
keytool -keystore kafka.server.keystore.jks -alias kafka-server -validity 365 \
  -genkey -keyalg RSA -keysize 4096 -storepass changeit -keypass changeit \
  -dname "CN=kafka.company.com, OU=IT, O=Company, L=Beijing, ST=Beijing, C=CN"
```

### 2. 访问控制

#### **RBAC权限模型**
```yaml
# Kafka ACL配置示例
users:
  - name: admin
    permissions:
      - resource: "*"
        operations: ["All"]
  
  - name: producer
    permissions:
      - resource: "Topic:logs-*"
        operations: ["Write", "Describe"]
      - resource: "Cluster"
        operations: ["Create"]
  
  - name: consumer
    permissions:
      - resource: "Topic:logs-*"
        operations: ["Read", "Describe"]
      - resource: "Group:*"
        operations: ["Read"]

# 应用ACL
kafka-acls.sh --authorizer-properties zookeeper.connect=localhost:2181 \
  --add --allow-principal User:producer \
  --operation Write --topic logs-*

kafka-acls.sh --authorizer-properties zookeeper.connect=localhost:2181 \
  --add --allow-principal User:consumer \
  --operation Read --topic logs-* --group consumer-group-*
```

#### **Elasticsearch安全配置**
```yaml
# elasticsearch.yml安全配置
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.keystore.path: certs/elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: certs/elastic-certificates.p12

# 角色定义
roles:
  logs_reader:
    indices:
      - names: ["logs-*"]
        privileges: ["read", "view_index_metadata"]
  
  logs_writer:
    indices:
      - names: ["logs-*"]
        privileges: ["write", "create_index", "view_index_metadata"]
  
  admin:
    cluster: ["all"]
    indices:
      - names: ["*"]
        privileges: ["all"]
```

## 运维最佳实践

### 1. 自动化部署

#### **Ansible Playbook示例**
```yaml
# deploy.yml
---
- name: Deploy Middleware Cluster
  hosts: all
  become: yes
  vars:
    java_version: "17"
    cluster_name: "bigdata-cluster"
    
  tasks:
    - name: Install Java
      package:
        name: "openjdk-{{ java_version }}-jdk"
        state: present
    
    - name: Create directories
      file:
        path: "{{ item }}"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: '0755'
      loop:
        - /export/server
        - /export/data
        - /export/logs
        - /export/backup
    
    - name: Configure system limits
      pam_limits:
        domain: "*"
        limit_type: "{{ item.type }}"
        limit_item: "{{ item.item }}"
        value: "{{ item.value }}"
      loop:
        - { type: 'soft', item: 'nofile', value: '65536' }
        - { type: 'hard', item: 'nofile', value: '65536' }
        - { type: 'soft', item: 'nproc', value: '32768' }
        - { type: 'hard', item: 'nproc', value: '32768' }
    
    - name: Configure sysctl
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        reload: yes
      loop:
        - { name: 'vm.max_map_count', value: '262144' }
        - { name: 'vm.swappiness', value: '1' }
        - { name: 'net.core.somaxconn', value: '65535' }

- name: Deploy Kafka
  hosts: kafka
  roles:
    - kafka
  vars:
    kafka_version: "2.8.1"
    kafka_scala_version: "2.13"
```

### 2. 监控和告警

#### **监控指标体系**
```yaml
# 监控指标分层
infrastructure:
  - cpu_usage
  - memory_usage
  - disk_usage
  - network_io
  - disk_io

middleware:
  kafka:
    - broker_status
    - message_rate
    - consumer_lag
    - partition_count
  
  elasticsearch:
    - cluster_health
    - index_rate
    - search_rate
    - jvm_heap_usage
  
  zookeeper:
    - connection_count
    - latency
    - leader_status

application:
  - request_rate
  - response_time
  - error_rate
  - business_metrics

# 告警阈值配置
thresholds:
  critical:
    cpu_usage: 90
    memory_usage: 95
    disk_usage: 95
    service_down: 1
  
  warning:
    cpu_usage: 80
    memory_usage: 85
    disk_usage: 85
    high_latency: 1000ms
```

### 3. 备份和恢复

#### **备份策略**
```bash
#!/bin/bash
# comprehensive_backup.sh

BACKUP_ROOT="/export/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_ROOT/$DATE"

mkdir -p $BACKUP_DIR

# 1. 配置文件备份
echo "Backing up configurations..."
tar -czf $BACKUP_DIR/configs.tar.gz \
  /export/server/*/config/ \
  /etc/systemd/system/kafka.service \
  /etc/systemd/system/elasticsearch.service \
  /etc/systemd/system/zookeeper.service

# 2. ZooKeeper数据备份
echo "Backing up ZooKeeper data..."
systemctl stop zookeeper
tar -czf $BACKUP_DIR/zookeeper_data.tar.gz /export/data/zookeeper/
systemctl start zookeeper

# 3. Kafka元数据备份
echo "Backing up Kafka metadata..."
kafka-topics.sh --bootstrap-server localhost:9092 --list > $BACKUP_DIR/kafka_topics.txt
kafka-consumer-groups.sh --bootstrap-server localhost:9092 --list > $BACKUP_DIR/kafka_groups.txt

# 4. Elasticsearch快照
echo "Creating Elasticsearch snapshot..."
curl -X PUT "localhost:9200/_snapshot/backup_repo/snapshot_$DATE?wait_for_completion=true"

# 5. 系统信息备份
echo "Backing up system information..."
cat > $BACKUP_DIR/system_info.txt << EOF
Hostname: $(hostname)
OS: $(cat /etc/os-release | grep PRETTY_NAME)
Kernel: $(uname -r)
Java: $(java -version 2>&1 | head -1)
Disk Usage: $(df -h)
Memory: $(free -h)
Network: $(ip addr show)
EOF

# 6. 清理旧备份
find $BACKUP_ROOT -type d -mtime +30 -exec rm -rf {} \;

echo "Backup completed: $BACKUP_DIR"
```

### 4. 容量管理

#### **自动清理脚本**
```bash
#!/bin/bash
# cleanup.sh

# 清理Kafka日志
echo "Cleaning up Kafka logs..."
find /export/data/kafka/logs -name "*.log" -mtime +7 -delete
find /export/data/kafka/logs -name "*.index" -mtime +7 -delete
find /export/data/kafka/logs -name "*.timeindex" -mtime +7 -delete

# 清理Elasticsearch旧索引
echo "Cleaning up Elasticsearch indices..."
curl -X DELETE "localhost:9200/logs-*-$(date -d '30 days ago' +%Y.%m.%d)"

# 清理系统日志
echo "Cleaning up system logs..."
journalctl --vacuum-time=30d
find /var/log -name "*.log" -mtime +30 -delete

# 清理临时文件
echo "Cleaning up temporary files..."
find /tmp -type f -mtime +7 -delete
find /export/logs -name "*.log.*" -mtime +7 -delete

echo "Cleanup completed"
```

## 故障预防

### 1. 健康检查

#### **全面健康检查脚本**
```bash
#!/bin/bash
# health_check.sh

REPORT_FILE="/tmp/health_report_$(date +%Y%m%d_%H%M%S).txt"

{
echo "=== 系统健康检查报告 ==="
echo "检查时间: $(date)"
echo "主机名: $(hostname)"
echo ""

# 1. 系统资源检查
echo "1. 系统资源状态:"
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo "内存使用率:"
free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

echo "磁盘使用率:"
df -h | awk '$5 > 80 {print "WARNING: " $0}'

echo "负载平均值:"
uptime

echo ""

# 2. 服务状态检查
echo "2. 服务状态:"
services=("zookeeper" "kafka" "elasticsearch")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✓ $service: 运行中"
    else
        echo "✗ $service: 未运行"
    fi
done

echo ""

# 3. 端口监听检查
echo "3. 端口监听状态:"
ports=(2181 9092 9200)
for port in "${ports[@]}"; do
    if netstat -tlnp | grep -q ":$port "; then
        echo "✓ 端口 $port: 监听中"
    else
        echo "✗ 端口 $port: 未监听"
    fi
done

echo ""

# 4. 集群状态检查
echo "4. 集群状态:"

# ZooKeeper状态
echo "ZooKeeper状态:"
echo "ruok" | nc localhost 2181

# Kafka状态
echo "Kafka Broker数量:"
kafka-broker-api-versions.sh --bootstrap-server localhost:9092 2>/dev/null | wc -l

# Elasticsearch状态
echo "Elasticsearch集群状态:"
curl -s localhost:9200/_cluster/health | jq '.status'

echo ""

# 5. 日志错误检查
echo "5. 最近错误日志:"
find /export/logs -name "*.log" -mtime -1 -exec grep -l "ERROR\|FATAL" {} \; | head -5

} > $REPORT_FILE

echo "健康检查完成，报告保存在: $REPORT_FILE"
cat $REPORT_FILE
```

### 2. 预警机制

#### **预警脚本**
```bash
#!/bin/bash
# early_warning.sh

# 检查磁盘空间
check_disk_space() {
    threshold=85
    df -h | awk -v thresh=$threshold '
    NR>1 && $5+0 > thresh {
        print "WARNING: Disk usage " $5 " on " $6 " exceeds " thresh "%"
    }'
}

# 检查内存使用
check_memory() {
    threshold=90
    mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $mem_usage -gt $threshold ]; then
        echo "WARNING: Memory usage ${mem_usage}% exceeds ${threshold}%"
    fi
}

# 检查CPU使用
check_cpu() {
    threshold=85
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d'.' -f1)
    if [ $cpu_usage -gt $threshold ]; then
        echo "WARNING: CPU usage ${cpu_usage}% exceeds ${threshold}%"
    fi
}

# 检查服务状态
check_services() {
    services=("zookeeper" "kafka" "elasticsearch")
    for service in "${services[@]}"; do
        if ! systemctl is-active --quiet $service; then
            echo "CRITICAL: Service $service is not running"
        fi
    done
}

# 执行检查
warnings=$(
    check_disk_space
    check_memory
    check_cpu
    check_services
)

# 发送告警
if [ -n "$warnings" ]; then
    echo "$warnings" | mail -s "System Warning: $(hostname)" <EMAIL>
    echo "$warnings"
fi
```

## 总结

最佳实践的核心原则：

1. **标准化**: 统一的配置、部署和运维标准
2. **自动化**: 减少人工操作，提高效率和可靠性
3. **监控**: 全面的监控体系和预警机制
4. **安全**: 多层次的安全防护措施
5. **性能**: 持续的性能优化和调优
6. **可靠性**: 完善的备份和恢复机制
7. **可扩展**: 支持水平扩展的架构设计
8. **文档化**: 完整的文档和知识管理

通过遵循这些最佳实践，可以构建一个稳定、高效、安全的大数据平台。
