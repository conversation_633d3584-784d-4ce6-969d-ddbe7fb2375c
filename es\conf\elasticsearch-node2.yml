# Elasticsearch 8.x 集群配置文件 - 节点2
# ***************

############################# Cluster Configuration #############################

# 集群名称（所有节点必须相同）
cluster.name: es-cluster-001

# 节点名称（每个节点不同）
node.name: es-node-2

# 节点角色配置
node.roles: [ master, data, ingest, ml, remote_cluster_client ]

############################# Network Configuration #############################

# 网络绑定地址
network.host: ***************

# HTTP端口
http.port: 9200

# 传输端口（节点间通信）
transport.port: 9300

############################# Discovery Configuration #############################

# 集群发现配置
discovery.seed_hosts: 
  - "***************:9300"
  - "***************:9300"
  - "***************:9300"

# 集群初始化主节点
cluster.initial_master_nodes:
  - "es-node-1"
  - "es-node-2"
  - "es-node-3"

############################# Path Configuration #############################

# 数据目录
path.data: /export/data/elasticsearch/data

# 日志目录
path.logs: /export/logs/elasticsearch

# 临时目录
path.repo: /export/data/elasticsearch/backup

############################# Memory Configuration #############################

# 禁用内存交换
bootstrap.memory_lock: true

############################# Security Configuration #############################

# 启用安全功能
xpack.security.enabled: true

# 传输层安全
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.client_authentication: required
xpack.security.transport.ssl.keystore.path: certs/elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: certs/elastic-certificates.p12

# HTTP层安全（可选，用于HTTPS）
xpack.security.http.ssl.enabled: false

############################# Monitoring Configuration #############################

# 启用监控
xpack.monitoring.collection.enabled: true

############################# Performance Configuration #############################

# 索引缓冲区大小
indices.memory.index_buffer_size: 20%

# 字段数据缓存大小
indices.fielddata.cache.size: 30%

# 查询缓存大小
indices.queries.cache.size: 10%

# 请求缓存大小
indices.requests.cache.size: 2%

# 线程池配置
thread_pool:
  write:
    size: 2
    queue_size: 1000
  search:
    size: 13
    queue_size: 1000

############################# Index Configuration #############################

# 默认分片数
index.number_of_shards: 3

# 默认副本数
index.number_of_replicas: 1

# 自动创建索引
action.auto_create_index: true

# 删除索引确认
action.destructive_requires_name: true

############################# Advanced Configuration #############################

# 集群健康检查
cluster.routing.allocation.disk.threshold_enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
cluster.routing.allocation.disk.watermark.flood_stage: 95%

# 分片分配
cluster.routing.allocation.same_shard.host: false

# 恢复设置
cluster.routing.allocation.node_concurrent_recoveries: 2
cluster.routing.allocation.node_initial_primaries_recoveries: 4

# 网络设置
transport.tcp.compress: true
http.compression: true
http.max_content_length: 100mb

# 搜索设置
search.max_buckets: 65536
