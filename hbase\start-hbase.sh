#!/bin/bash

# HBase启动脚本 - 适配ZooKeeper SASL认证

echo "=== 启动HBase集群 ==="

# 设置环境变量
source /export/server/hbase/hbase/conf/hbase-env.sh

# 检查Java环境
if [ ! -d "$JAVA_HOME" ]; then
    echo "错误: JAVA_HOME未设置或路径不存在: $JAVA_HOME"
    exit 1
fi

# 检查ZooKeeper连接
echo "1. 检查ZooKeeper连接..."
for zk_server in 192.168.200.101 192.168.200.102 192.168.200.103; do
    if echo ruok | nc -w 3 $zk_server 4181 | grep -q imok; then
        echo "✓ ZooKeeper $zk_server:4181 运行正常"
    else
        echo "✗ ZooKeeper $zk_server:4181 连接失败"
    fi
done

# 检查本地数据目录（单机模式）
echo ""
echo "2. 检查本地数据目录..."
if [ -d "/export/data/hbase" ]; then
    echo "✓ 本地HBase数据目录存在"
else
    echo "⚠ 本地HBase数据目录不存在，正在创建..."
    mkdir -p /export/data/hbase
    chown -R appuser:appuser /export/data/hbase
fi

# 集群模式HDFS检查（注释）
# echo ""
# echo "2. 检查HDFS连接..."
# if hadoop fs -test -d /hbase 2>/dev/null; then
#     echo "✓ HDFS /hbase目录存在"
# else
#     echo "⚠ HDFS /hbase目录不存在，正在创建..."
#     hadoop fs -mkdir -p /hbase
#     hadoop fs -chown hbase:hbase /hbase
# fi

# 创建必要目录
echo ""
echo "3. 创建本地目录..."
mkdir -p /export/logs/hbase
mkdir -p /export/data/hbase
mkdir -p /export/server/hbase/hbase/pids
chown -R appuser:appuser /export/logs/hbase
chown -R appuser:appuser /export/data/hbase
chown -R appuser:appuser /export/server/hbase/hbase/pids

# 启动HBase Master
echo ""
echo "4. 启动HBase Master..."
/export/server/hbase/hbase/bin/hbase-daemon.sh start master

# 等待Master启动
echo "等待Master启动..."
sleep 10

# 单机模式不需要单独启动RegionServers（Master会自动启动）
echo ""
echo "5. 单机模式 - RegionServer会随Master自动启动"

# 集群模式RegionServer启动（注释）
# echo ""
# echo "5. 启动RegionServers..."
# su - appuser -c "/export/server/hbase/hbase/bin/hbase-daemons.sh start regionserver"
#
# # 等待RegionServers启动
# echo "等待RegionServers启动..."
# sleep 10

# 检查启动状态
echo ""
echo "6. 检查HBase服务状态..."
echo "HBase进程:"
ps aux | grep -E "(HMaster|HRegionServer)" | grep -v grep

echo ""
echo "端口监听:"
netstat -tlnp | grep -E ":(16000|16010|16020|16030)" || echo "未找到HBase监听端口"

echo ""
echo "=== HBase启动完成 ==="
echo ""
echo "访问地址:"
echo "  HBase Master Web UI: http://192.168.200.101:16010"
echo "  HBase Shell: /export/server/hbase/hbase/bin/hbase shell"
