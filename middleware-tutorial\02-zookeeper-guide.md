# ZooKeeper详解教程

## 什么是ZooKeeper？

ZooKeeper是一个开源的分布式协调服务，为分布式应用提供一致性服务。它是许多分布式系统的基础组件。

### 核心概念

#### **1. 数据模型**
```
ZooKeeper的数据模型类似于文件系统的树形结构：

/
├── zookeeper (系统节点)
├── brokers (Kafka broker信息)
│   ├── ids
│   │   ├── 0
│   │   ├── 1
│   │   └── 2
│   └── topics
├── consumers (消费者信息)
├── config (配置信息)
└── admin (管理信息)
```

#### **2. 节点类型 (ZNode)**
```
持久节点 (Persistent):
- 创建后一直存在，直到主动删除
- 用于存储配置信息

临时节点 (Ephemeral):
- 会话结束时自动删除
- 用于服务注册和发现

顺序节点 (Sequential):
- 自动在节点名后添加序号
- 用于分布式锁和队列
```

#### **3. 监听机制 (Watcher)**
```
客户端可以在节点上设置监听器：
- 节点数据变化
- 子节点变化
- 节点创建/删除

特点：
- 一次性触发
- 异步通知
- 有序保证
```

## 为什么需要ZooKeeper？

### 1. 分布式系统的挑战

#### **配置管理**
```
问题: 分布式系统中配置如何统一管理？
解决: ZooKeeper提供集中式配置存储

示例:
/config/database/url = "jdbc:mysql://..."
/config/database/timeout = "30000"
```

#### **服务发现**
```
问题: 服务实例动态变化，如何发现可用服务？
解决: 服务注册到ZooKeeper，客户端监听变化

示例:
/services/user-service/192.168.1.10:8080
/services/user-service/192.168.1.11:8080
```

#### **分布式锁**
```
问题: 多个进程如何协调访问共享资源？
解决: 利用ZooKeeper的顺序临时节点实现

示例:
/locks/resource1/lock-0000000001
/locks/resource1/lock-0000000002
```

#### **Leader选举**
```
问题: 分布式系统中如何选择主节点？
解决: 利用ZooKeeper的一致性保证选举

示例:
/election/leader -> 192.168.1.10
```

### 2. ZooKeeper的优势

#### **强一致性**
```
通过Zab协议保证：
- 顺序一致性: 操作按顺序执行
- 原子性: 操作要么成功要么失败
- 单一视图: 客户端看到一致的数据
```

#### **高可用性**
```
通过集群部署保证：
- 多数派原则: 超过半数节点存活即可服务
- 自动故障转移: Leader故障时自动选举新Leader
- 数据复制: 所有节点保存完整数据副本
```

#### **高性能**
```
优化设计：
- 读操作可以在任意节点执行
- 写操作通过Leader协调
- 内存存储: 数据保存在内存中
```

## ZooKeeper集群架构

### 1. 角色分工

#### **Leader**
```
职责:
- 处理所有写请求
- 协调事务提交
- 维护集群状态

特点:
- 集群中只有一个Leader
- 通过选举产生
- 故障时重新选举
```

#### **Follower**
```
职责:
- 处理读请求
- 参与Leader选举
- 参与事务投票

特点:
- 可以有多个Follower
- 同步Leader的数据
- 可以成为Leader候选者
```

#### **Observer (可选)**
```
职责:
- 处理读请求
- 同步数据但不参与投票

特点:
- 不参与Leader选举
- 不影响写性能
- 用于扩展读能力
```

### 2. Zab协议详解

#### **协议阶段**
```
1. Leader选举阶段:
   - 节点启动时进入选举状态
   - 投票选择Leader
   - 多数派达成一致

2. 数据同步阶段:
   - 新Leader同步数据到Follower
   - 确保数据一致性
   - 完成后进入广播阶段

3. 消息广播阶段:
   - Leader接收写请求
   - 广播给所有Follower
   - 收到多数派确认后提交
```

#### **选举算法**
```
选举条件:
1. 更大的epoch (选举轮次)
2. 更大的zxid (事务ID)
3. 更大的server id

示例:
Server 1: epoch=1, zxid=100, id=1
Server 2: epoch=1, zxid=101, id=2  ← 当选Leader
Server 3: epoch=1, zxid=100, id=3
```

## 配置详解

### 1. 核心配置参数

#### **基础配置**
```properties
# 数据目录
dataDir=/export/data/zookeeper

# 日志目录  
dataLogDir=/export/logs/zookeeper

# 客户端端口
clientPort=2181

# 心跳间隔 (毫秒)
tickTime=2000

# Follower连接Leader超时时间 (tick数)
initLimit=10

# Follower同步Leader超时时间 (tick数)  
syncLimit=5
```

#### **集群配置**
```properties
# 服务器列表
server.1=***************:2888:3888
server.2=***************:2888:3888  
server.3=***************:2888:3888

格式说明:
server.id=host:port1:port2
- id: 服务器ID
- host: 服务器地址
- port1: Follower连接Leader端口
- port2: Leader选举端口
```

#### **性能调优配置**
```properties
# 最大客户端连接数
maxClientCnxns=60

# 会话超时时间范围
minSessionTimeout=4000
maxSessionTimeout=40000

# 快照保留数量
autopurge.snapRetainCount=3

# 自动清理间隔 (小时)
autopurge.purgeInterval=1

# 预分配事务日志大小 (KB)
preAllocSize=65536

# 事务日志同步频率
fsync.warningthresholdms=1000
```

### 2. JVM配置

#### **内存设置**
```bash
# 为什么设置1GB堆内存？
export JVMFLAGS="-Xmx1024m -Xms1024m"

原因:
1. ZooKeeper数据量通常不大
2. 主要存储元数据信息
3. 过大的堆会影响GC性能
4. 1GB足够支撑大多数场景
```

#### **GC配置**
```bash
# 为什么使用G1GC？
export JVMFLAGS="$JVMFLAGS -XX:+UseG1GC"

原因:
1. 低延迟: 可预测的暂停时间
2. 适合服务端应用
3. 自动调优能力强
4. 对大堆支持好
```

### 3. 安全配置

#### **SASL认证**
```properties
# 启用SASL认证
authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider

# Kerberos配置
jaasLoginRenew=3600000
kerberos.removeHostFromPrincipal=true
kerberos.removeRealmFromPrincipal=true
```

#### **ACL权限控制**
```java
// 设置节点权限
zk.create("/secure-node", data, 
    Arrays.asList(
        new ACL(Perms.ALL, new Id("digest", "user:password")),
        new ACL(Perms.READ, new Id("world", "anyone"))
    ), 
    CreateMode.PERSISTENT);
```

## 运维最佳实践

### 1. 监控指标

#### **关键指标**
```
连接数:
- zk_num_alive_connections
- zk_max_client_cnxns

延迟:
- zk_avg_latency
- zk_max_latency
- zk_min_latency

吞吐量:
- zk_packets_received
- zk_packets_sent

存储:
- zk_approximate_data_size
- zk_open_file_descriptor_count
```

#### **监控命令**
```bash
# 四字命令监控
echo "stat" | nc localhost 2181  # 服务器状态
echo "conf" | nc localhost 2181  # 配置信息
echo "cons" | nc localhost 2181  # 连接信息
echo "dump" | nc localhost 2181  # 会话信息
echo "envi" | nc localhost 2181  # 环境信息
echo "ruok" | nc localhost 2181  # 健康检查
echo "srst" | nc localhost 2181  # 重置统计
echo "srvr" | nc localhost 2181  # 服务器信息
```

### 2. 故障排查

#### **常见问题**
```
1. Leader选举失败:
   原因: 网络分区、时钟不同步
   解决: 检查网络连通性、同步时钟

2. 连接超时:
   原因: 网络延迟、负载过高
   解决: 调整超时参数、优化网络

3. 磁盘空间不足:
   原因: 事务日志和快照文件过多
   解决: 启用自动清理、增加磁盘空间

4. 内存不足:
   原因: 连接数过多、数据量过大
   解决: 增加内存、限制连接数
```

#### **日志分析**
```bash
# 查看选举日志
grep "LEADING\|FOLLOWING\|LOOKING" zookeeper.log

# 查看连接日志
grep "Established session" zookeeper.log

# 查看错误日志
grep "ERROR\|WARN" zookeeper.log
```

### 3. 备份恢复

#### **数据备份**
```bash
# 备份数据目录
tar -czf zk-backup-$(date +%Y%m%d).tar.gz /export/data/zookeeper

# 备份配置文件
cp /export/server/zookeeper/conf/zoo.cfg /backup/
```

#### **数据恢复**
```bash
# 停止ZooKeeper
zkServer.sh stop

# 恢复数据
tar -xzf zk-backup-20240101.tar.gz -C /

# 启动ZooKeeper
zkServer.sh start
```

## 性能调优

### 1. 系统级调优

#### **文件描述符**
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf
```

#### **网络参数**
```bash
# 调整TCP参数
echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" >> /etc/sysctl.conf
sysctl -p
```

### 2. 应用级调优

#### **连接池配置**
```java
// 客户端连接池配置
ZooKeeper zk = new ZooKeeper(
    "***************:2181,***************:2181,***************:2181",
    30000,  // 会话超时时间
    watcher
);
```

#### **批量操作**
```java
// 使用事务批量操作
Transaction txn = zk.transaction();
txn.create("/path1", data1, acl, CreateMode.PERSISTENT);
txn.create("/path2", data2, acl, CreateMode.PERSISTENT);
txn.setData("/path3", data3, -1);
txn.commit();
```

## 总结

ZooKeeper作为分布式协调服务的核心组件，其重要性体现在：

1. **一致性保证**: 通过Zab协议提供强一致性
2. **高可用性**: 通过集群部署避免单点故障
3. **简单易用**: 提供类似文件系统的API
4. **性能优秀**: 读多写少场景下性能优异
5. **生态丰富**: 被众多开源项目采用

理解ZooKeeper的工作原理和配置要点，对于构建可靠的分布式系统至关重要。
