#!/bin/bash

# HBase重启脚本 - 确保SASL配置生效

echo "=== HBase SASL重启脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo "1. 停止HBase服务..."
./hbase/stop-hbase.sh

echo ""
echo "2. 等待进程完全停止..."
sleep 10

# 确保所有HBase进程都已停止
remaining_processes=$(ps aux | grep -E "(HMaster|HRegionServer)" | grep -v grep)
if [ -n "$remaining_processes" ]; then
    echo -e "${YELLOW}⚠ 发现残留进程，强制终止...${NC}"
    pkill -f HMaster
    pkill -f HRegionServer
    sleep 5
fi

echo ""
echo "3. 复制最新配置文件..."
if [ -d "/export/server/hbase/hbase/conf" ]; then
    echo "  复制配置文件到HBase安装目录..."
    cp hbase/conf/* /export/server/hbase/hbase/conf/
    echo -e "  ${GREEN}✓${NC} 配置文件复制完成"
else
    echo -e "  ${RED}✗${NC} HBase配置目录不存在: /export/server/hbase/hbase/conf"
    echo "  请确认HBase安装路径是否正确"
    exit 1
fi

echo ""
echo "4. 验证关键配置..."

# 检查JAAS配置文件
if [ -f "/export/server/hbase/hbase/conf/jaas.conf" ]; then
    echo -e "  ${GREEN}✓${NC} JAAS配置文件存在"
else
    echo -e "  ${RED}✗${NC} JAAS配置文件缺失"
    exit 1
fi

# 检查hbase-site.xml中的SASL配置
if grep -q "zookeeper.sasl.client" /export/server/hbase/hbase/conf/hbase-site.xml; then
    echo -e "  ${GREEN}✓${NC} ZooKeeper SASL客户端配置存在"
else
    echo -e "  ${YELLOW}⚠${NC} ZooKeeper SASL客户端配置可能缺失"
fi

echo ""
echo "5. 检查ZooKeeper连接..."
zk_ok=true
for zk_server in 192.168.200.101 192.168.200.102 192.168.200.103; do
    if echo ruok | nc -w 3 $zk_server 4181 | grep -q imok; then
        echo -e "  ${GREEN}✓${NC} ZooKeeper $zk_server 运行正常"
    else
        echo -e "  ${RED}✗${NC} ZooKeeper $zk_server 连接失败"
        zk_ok=false
    fi
done

if [ "$zk_ok" = false ]; then
    echo -e "${RED}错误: ZooKeeper集群不可用，请先启动ZooKeeper${NC}"
    exit 1
fi

echo ""
echo "6. 启动HBase服务..."
./hbase/start-hbase.sh

echo ""
echo "7. 等待服务启动..."
sleep 15

echo ""
echo "8. 验证SASL认证状态..."

# 检查HBase进程
hbase_processes=$(ps aux | grep HMaster | grep -v grep)
if [ -n "$hbase_processes" ]; then
    echo -e "  ${GREEN}✓${NC} HBase Master进程运行中"
    
    # 检查进程的SASL相关参数
    master_pid=$(ps aux | grep HMaster | grep -v grep | awk '{print $2}' | head -1)
    if [ -n "$master_pid" ]; then
        echo "    检查Master进程 (PID: $master_pid) 的SASL配置..."
        
        if ps -p $master_pid -o args --no-headers | grep -q "java.security.auth.login.config"; then
            echo -e "      ${GREEN}✓${NC} JAAS配置已加载"
        else
            echo -e "      ${RED}✗${NC} JAAS配置未加载"
        fi
        
        if ps -p $master_pid -o args --no-headers | grep -q "zookeeper.sasl.client=true"; then
            echo -e "      ${GREEN}✓${NC} ZooKeeper SASL客户端已启用"
        else
            echo -e "      ${RED}✗${NC} ZooKeeper SASL客户端未启用"
        fi
    fi
else
    echo -e "  ${RED}✗${NC} HBase Master进程未运行"
    exit 1
fi

echo ""
echo "9. 检查最新日志..."
log_dir="/export/logs/hbase"
if [ -d "$log_dir" ]; then
    latest_log=$(ls -t $log_dir/hbase-*.log 2>/dev/null | head -1)
    if [ -n "$latest_log" ]; then
        echo "  检查最新日志: $latest_log"
        
        # 检查最近的SASL相关日志
        recent_sasl=$(tail -50 "$latest_log" | grep -i "sasl\|auth" | tail -3)
        if [ -n "$recent_sasl" ]; then
            echo "    最近的认证日志:"
            echo "$recent_sasl" | sed 's/^/      /'
        fi
        
        # 检查是否有错误
        recent_errors=$(tail -20 "$latest_log" | grep -i "error\|exception")
        if [ -n "$recent_errors" ]; then
            echo -e "    ${RED}最近的错误:${NC}"
            echo "$recent_errors" | sed 's/^/      /'
        else
            echo -e "    ${GREEN}✓${NC} 未发现明显错误"
        fi
    fi
fi

echo ""
echo "10. 测试HBase连接..."
if [ -f "/export/server/hbase/hbase/bin/hbase" ]; then
    echo "  测试HBase Shell连接..."
    timeout 10 /export/server/hbase/hbase/bin/hbase shell <<< "list" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "  ${GREEN}✓${NC} HBase Shell连接成功"
    else
        echo -e "  ${YELLOW}⚠${NC} HBase Shell连接测试超时或失败"
    fi
fi

echo ""
echo "=== HBase SASL重启完成 ==="
echo ""
echo "Web界面访问: http://192.168.200.101:16010"
echo "查看日志: tail -f /export/logs/hbase/hbase-*.log"
echo "运行诊断: ./hbase/diagnose-sasl.sh"
