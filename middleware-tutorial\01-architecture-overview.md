# 大数据架构总览

## 整体架构图

```mermaid
graph TB
    subgraph "应用层"
        A1[Web应用] --> A2[API网关]
        A3[微服务] --> A2
        A4[数据分析] --> A2
    end
    
    subgraph "消息层"
        B1[Kafka Broker 1] 
        B2[Kafka Broker 2]
        B3[Kafka Broker 3]
    end
    
    subgraph "协调层"
        C1[ZooKeeper 1]
        C2[ZooKeeper 2] 
        C3[ZooKeeper 3]
    end
    
    subgraph "存储层"
        D1[Elasticsearch 1]
        D2[Elasticsearch 2]
        D3[Elasticsearch 3]
    end
    
    subgraph "基础设施"
        E1[负载均衡器]
        E2[监控系统]
        E3[日志收集]
    end
    
    A2 --> B1
    A2 --> B2
    A2 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    B1 --> D1
    B2 --> D2
    B3 --> D3
    
    E1 --> A2
    E2 --> B1
    E2 --> B2
    E2 --> B3
    E3 --> D1
    E3 --> D2
    E3 --> D3
```

## 架构分层说明

### 1. 应用层 (Application Layer)

#### **职责**
- 业务逻辑处理
- 用户接口提供
- 数据处理和分析

#### **组件**
- **Web应用**: 用户界面和交互
- **API网关**: 统一入口和路由
- **微服务**: 业务功能模块
- **数据分析**: 实时和离线分析

#### **为什么这样设计？**
```
1. 解耦: 应用层与基础设施分离
2. 扩展: 可以独立扩展不同的应用组件
3. 维护: 业务逻辑变更不影响基础设施
4. 复用: 基础设施可以支持多个应用
```

### 2. 消息层 (Message Layer)

#### **职责**
- 异步消息传递
- 数据流处理
- 系统解耦

#### **Kafka集群架构**
```
Broker 1 (***************:9092)
├── Topic A (Partition 0, 1)
├── Topic B (Partition 2)
└── Controller Role

Broker 2 (***************:9092)  
├── Topic A (Partition 2)
├── Topic B (Partition 0, 1)
└── Controller Role

Broker 3 (***************:9092)
├── Topic A (Partition 1, 2)
├── Topic B (Partition 0)
└── Controller Role
```

#### **为什么选择Kafka？**
```
1. 高吞吐量: 单机可达百万级TPS
2. 低延迟: 毫秒级消息传递
3. 持久化: 消息可靠存储
4. 分布式: 天然支持集群部署
5. 流处理: 内置流处理能力
```

### 3. 协调层 (Coordination Layer)

#### **职责**
- 分布式协调
- 配置管理
- 服务发现
- 分布式锁

#### **ZooKeeper集群架构**
```
ZooKeeper 1 (***************:2181) - Leader
├── /brokers (Kafka节点信息)
├── /consumers (消费者信息)
├── /config (配置信息)
└── /admin (管理信息)

ZooKeeper 2 (***************:2181) - Follower
└── 同步Leader数据

ZooKeeper 3 (***************:2181) - Follower  
└── 同步Leader数据
```

#### **为什么需要ZooKeeper？**
```
1. 一致性: 通过Zab协议保证强一致性
2. 可靠性: 多数派机制保证服务可用
3. 实时性: 监听机制提供实时通知
4. 简单性: 提供简单的API接口
```

### 4. 存储层 (Storage Layer)

#### **职责**
- 数据存储和索引
- 全文搜索
- 实时分析
- 日志聚合

#### **Elasticsearch集群架构**
```
ES Node 1 (***************:9200) - Master/Data
├── Index A (Shard 0, 1)
├── Index B (Shard 2)
└── 集群管理

ES Node 2 (***************:9200) - Master/Data
├── Index A (Shard 2, Replica 0)
├── Index B (Shard 0, 1)
└── 数据存储

ES Node 3 (***************:9200) - Master/Data
├── Index A (Shard 1, Replica 2)
├── Index B (Replica 1, 2)
└── 搜索查询
```

#### **为什么选择Elasticsearch？**
```
1. 搜索能力: 强大的全文搜索功能
2. 实时性: 近实时的数据索引和查询
3. 分析能力: 内置聚合分析功能
4. 可扩展: 水平扩展能力
5. 生态系统: 丰富的插件和工具
```

## 数据流向分析

### 1. 写入流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant Kafka as Kafka集群
    participant ZK as ZooKeeper
    participant ES as Elasticsearch
    
    App->>Kafka: 1. 发送消息
    Kafka->>ZK: 2. 更新元数据
    Kafka->>Kafka: 3. 复制到副本
    Kafka->>App: 4. 确认写入
    Kafka->>ES: 5. 流式传输(可选)
    ES->>ES: 6. 索引数据
```

#### **流程说明**
1. **应用发送**: 应用通过Producer发送消息到Kafka
2. **元数据更新**: Kafka更新ZooKeeper中的元数据信息
3. **数据复制**: 消息复制到多个副本保证可靠性
4. **确认返回**: 向应用返回写入确认
5. **流式传输**: 通过Kafka Connect传输到Elasticsearch
6. **数据索引**: Elasticsearch索引数据供搜索使用

### 2. 读取流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant Kafka as Kafka集群
    participant ES as Elasticsearch
    
    App->>Kafka: 1. 消费消息
    Kafka->>App: 2. 返回消息
    App->>ES: 3. 搜索查询
    ES->>App: 4. 返回结果
```

#### **流程说明**
1. **消息消费**: 应用通过Consumer从Kafka消费消息
2. **消息返回**: Kafka返回消息给应用处理
3. **搜索查询**: 应用向Elasticsearch发起搜索请求
4. **结果返回**: Elasticsearch返回搜索结果

## 网络拓扑设计

### 1. 物理网络

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Node 1        │    │   Node 2        │    │   Node 3        │
│ *************** │    │ *************** │    │ *************** │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ ZooKeeper:2181  │◄──►│ ZooKeeper:2181  │◄──►│ ZooKeeper:2181  │
│ Kafka:9092      │◄──►│ Kafka:9092      │◄──►│ Kafka:9092      │
│ ES:9200         │◄──►│ ES:9200         │◄──►│ ES:9200         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Load Balancer  │
                    │ 192.168.200.100 │
                    └─────────────────┘
                             ▲
                             │
                    ┌─────────────────┐
                    │   Client Apps   │
                    └─────────────────┘
```

### 2. 端口规划

| 服务 | 端口 | 用途 | 访问范围 |
|------|------|------|----------|
| ZooKeeper | 2181 | 客户端连接 | 内网 |
| ZooKeeper | 2888 | Follower连接 | 集群内 |
| ZooKeeper | 3888 | Leader选举 | 集群内 |
| Kafka | 9092 | Broker通信 | 内网 |
| Kafka | 9093 | Controller通信 | 集群内 |
| Elasticsearch | 9200 | HTTP API | 内网 |
| Elasticsearch | 9300 | 节点通信 | 集群内 |

### 3. 防火墙规则

```bash
# 允许集群内通信
iptables -A INPUT -s *************/24 -j ACCEPT

# 允许特定端口访问
iptables -A INPUT -p tcp --dport 2181 -j ACCEPT  # ZooKeeper
iptables -A INPUT -p tcp --dport 9092 -j ACCEPT  # Kafka
iptables -A INPUT -p tcp --dport 9200 -j ACCEPT  # Elasticsearch

# 拒绝其他访问
iptables -A INPUT -j DROP
```

## 容量规划

### 1. 硬件配置建议

#### **小型环境 (开发/测试)**
```
CPU: 4核
内存: 8GB
存储: 100GB SSD
网络: 1Gbps

适用场景:
- 开发环境
- 功能测试
- 小规模POC
```

#### **中型环境 (生产)**
```
CPU: 8核
内存: 16GB
存储: 500GB SSD
网络: 10Gbps

适用场景:
- 中小企业生产环境
- 日处理量TB级
- 并发用户数千
```

#### **大型环境 (企业级)**
```
CPU: 16核+
内存: 32GB+
存储: 1TB+ NVMe SSD
网络: 25Gbps+

适用场景:
- 大型企业
- 日处理量PB级
- 并发用户数万
```

### 2. 存储规划

#### **数据增长预估**
```
Kafka数据保留: 7天 (可配置)
Elasticsearch索引: 30天 (可配置)
日志文件: 30天轮转

计算公式:
总存储 = (日均数据量 × 保留天数 × 副本数) × 1.5(预留空间)
```

#### **IOPS需求**
```
ZooKeeper: 1000 IOPS (主要是随机读写)
Kafka: 10000+ IOPS (顺序写为主)
Elasticsearch: 5000+ IOPS (混合读写)
```

## 高可用设计

### 1. 故障场景分析

#### **单节点故障**
```
影响: 服务降级但不中断
恢复: 自动故障转移
时间: 秒级

处理机制:
- ZooKeeper: 重新选举Leader
- Kafka: 分区重新分配
- Elasticsearch: 分片重新分配
```

#### **网络分区**
```
影响: 可能出现脑裂
恢复: 网络恢复后自动同步
时间: 分钟级

处理机制:
- 多数派原则
- 分区容忍性
- 数据一致性检查
```

#### **数据中心故障**
```
影响: 服务中断
恢复: 手动切换到备用数据中心
时间: 小时级

处理机制:
- 异地备份
- 灾难恢复计划
- 数据同步策略
```

### 2. 监控指标

#### **系统级监控**
```
CPU使用率: < 80%
内存使用率: < 85%
磁盘使用率: < 90%
网络带宽: < 80%
```

#### **应用级监控**
```
ZooKeeper:
- 连接数
- 延迟时间
- 事务数

Kafka:
- 吞吐量
- 延迟时间
- 消费滞后

Elasticsearch:
- 查询QPS
- 索引速度
- 集群状态
```

## 总结

这种架构设计的核心思想是：

1. **分层解耦**: 每一层专注于特定功能
2. **水平扩展**: 通过增加节点提升性能
3. **高可用**: 通过冗余和故障转移保证可用性
4. **一致性**: 通过分布式协议保证数据一致性
5. **可观测**: 通过监控和日志保证系统可观测性

每个组件都有其特定的作用，组合在一起形成了一个完整的大数据处理平台。
