# 故障排查指南

## 故障排查方法论

### 1. 故障分类

#### **按影响范围分类**
```
系统级故障:
- 硬件故障 (CPU、内存、磁盘、网络)
- 操作系统故障
- 网络连接问题

服务级故障:
- 进程崩溃
- 配置错误
- 资源耗尽

应用级故障:
- 业务逻辑错误
- 数据不一致
- 性能问题
```

#### **按紧急程度分类**
```
P0 - 紧急 (服务完全不可用):
- 集群完全宕机
- 数据丢失
- 安全漏洞

P1 - 高优先级 (服务严重受影响):
- 部分节点故障
- 性能严重下降
- 功能异常

P2 - 中优先级 (服务轻微受影响):
- 告警触发
- 性能轻微下降
- 非关键功能异常

P3 - 低优先级 (不影响服务):
- 日志错误
- 监控异常
- 优化建议
```

### 2. 故障排查流程

```mermaid
graph TD
    A[故障发现] --> B[影响评估]
    B --> C[紧急处理]
    C --> D[根因分析]
    D --> E[解决方案]
    E --> F[验证修复]
    F --> G[总结归档]
    
    B --> H{是否紧急?}
    H -->|是| I[立即响应]
    H -->|否| J[正常流程]
```

## ZooKeeper故障排查

### 1. 常见故障场景

#### **Leader选举失败**
```bash
# 症状
echo "stat" | nc localhost 2181
# 返回: This ZooKeeper instance is not currently serving requests

# 排查步骤
1. 检查网络连通性
ping ***************
ping ***************

2. 检查端口监听
netstat -tlnp | grep -E ":(2181|2888|3888)"

3. 检查日志
tail -100 /export/logs/zookeeper/zookeeper.log | grep -i "election\|leader"

4. 检查时钟同步
ntpq -p

# 常见原因和解决方案
原因1: 网络分区
解决: 修复网络连接，确保节点间通信正常

原因2: 时钟不同步
解决: 配置NTP同步
systemctl enable ntp
systemctl start ntp

原因3: 磁盘空间不足
解决: 清理磁盘空间
df -h
rm -rf /export/data/zookeeper/version-2/log.*

原因4: 配置错误
解决: 检查myid文件和zoo.cfg配置
cat /export/data/zookeeper/myid
grep "server\." /export/server/zookeeper/conf/zoo.cfg
```

#### **连接超时**
```bash
# 症状
zkCli.sh -server localhost:2181
# 连接超时或拒绝连接

# 排查步骤
1. 检查ZooKeeper进程
ps aux | grep zookeeper
jps | grep QuorumPeerMain

2. 检查端口监听
ss -tlnp | grep 2181

3. 检查防火墙
iptables -L | grep 2181
firewall-cmd --list-ports

4. 检查连接数
echo "cons" | nc localhost 2181 | wc -l

# 解决方案
1. 重启ZooKeeper服务
zkServer.sh stop
zkServer.sh start

2. 调整连接数限制
# 在zoo.cfg中添加
maxClientCnxns=100

3. 清理无效连接
echo "cons" | nc localhost 2181 | grep -v "/"
```

### 2. 性能问题排查

#### **响应延迟高**
```bash
# 检查延迟
echo "mntr" | nc localhost 2181 | grep latency

# 检查负载
echo "mntr" | nc localhost 2181 | grep -E "(outstanding|queued)"

# 检查GC情况
jstat -gc $(pgrep -f QuorumPeerMain) 5s

# 优化建议
1. 调整JVM参数
export JVMFLAGS="-Xmx2g -Xms2g -XX:+UseG1GC"

2. 调整快照和日志清理
autopurge.snapRetainCount=5
autopurge.purgeInterval=24

3. 使用SSD存储
dataDir=/export/data/zookeeper
dataLogDir=/export/logs/zookeeper
```

## Kafka故障排查

### 1. 启动失败

#### **端口冲突**
```bash
# 症状
[ERROR] Address already in use (kafka.network.Acceptor)

# 排查
netstat -tlnp | grep -E ":(9092|9093)"
lsof -i :9092

# 解决
1. 停止占用端口的进程
kill -9 $(lsof -t -i:9092)

2. 修改配置端口
listeners=SASL_PLAINTEXT://***************:9094

3. 检查JMX端口冲突
unset JMX_PORT
# 或修改JMX端口
export JMX_PORT=9999
```

#### **权限问题**
```bash
# 症状
java.io.IOException: Permission denied

# 排查
ls -la /export/data/kafka/
ls -la /export/logs/kafka/

# 解决
chown -R kafka:kafka /export/data/kafka/
chown -R kafka:kafka /export/logs/kafka/
chmod -R 755 /export/data/kafka/
```

#### **内存不足**
```bash
# 症状
java.lang.OutOfMemoryError: Java heap space

# 排查
free -h
ps aux | grep kafka | grep -v grep

# 解决
1. 调整JVM堆内存
export KAFKA_HEAP_OPTS="-Xmx2G -Xms2G"

2. 调整页缓存
echo 'vm.dirty_ratio=5' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio=2' >> /etc/sysctl.conf
sysctl -p
```

### 2. 运行时问题

#### **消息丢失**
```bash
# 排查步骤
1. 检查acks配置
grep "acks" /export/server/kafka/config/server.properties

2. 检查副本数
kafka-topics.sh --describe --topic your-topic --bootstrap-server localhost:9092

3. 检查ISR状态
kafka-topics.sh --describe --topic your-topic --bootstrap-server localhost:9092 | grep "Isr:"

4. 检查日志保留
grep "log.retention" /export/server/kafka/config/server.properties

# 解决方案
1. 设置合适的acks
acks=all

2. 增加副本数
kafka-topics.sh --alter --topic your-topic --partitions 3 --replication-factor 3

3. 调整min.insync.replicas
min.insync.replicas=2
```

#### **消费滞后**
```bash
# 检查消费滞后
kafka-consumer-groups.sh --bootstrap-server localhost:9092 --describe --group your-group

# 分析原因
1. 消费者处理能力不足
   - 增加消费者实例
   - 优化消费逻辑
   - 调整批量大小

2. 网络问题
   - 检查网络延迟
   - 调整fetch参数

3. 分区不均衡
   - 重新分配分区
   - 调整分区策略

# 解决方案
1. 增加消费者
kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic your-topic --group your-group

2. 调整消费参数
fetch.min.bytes=1024
fetch.max.wait.ms=500
max.poll.records=1000

3. 重置偏移量
kafka-consumer-groups.sh --bootstrap-server localhost:9092 --group your-group --reset-offsets --to-latest --topic your-topic --execute
```

#### **分区不可用**
```bash
# 症状
org.apache.kafka.common.errors.NotLeaderForPartitionException

# 排查
1. 检查分区状态
kafka-topics.sh --describe --topic your-topic --bootstrap-server localhost:9092

2. 检查Broker状态
kafka-broker-api-versions.sh --bootstrap-server localhost:9092

3. 检查Controller状态
kafka-log-dirs.sh --bootstrap-server localhost:9092 --describe

# 解决
1. 重新选举Leader
kafka-leader-election.sh --bootstrap-server localhost:9092 --topic your-topic --partition 0

2. 重启Broker
systemctl restart kafka

3. 重新分配副本
kafka-reassign-partitions.sh --bootstrap-server localhost:9092 --reassignment-json-file reassignment.json --execute
```

## Elasticsearch故障排查

### 1. 集群状态异常

#### **集群状态为红色**
```bash
# 检查集群状态
curl -X GET "localhost:9200/_cluster/health?pretty"

# 检查分片状态
curl -X GET "localhost:9200/_cat/shards?v&h=index,shard,prirep,state,unassigned.reason"

# 检查未分配分片
curl -X GET "localhost:9200/_cluster/allocation/explain?pretty"

# 常见原因和解决方案
1. 主分片丢失
   - 检查节点状态
   - 恢复数据或重新索引

2. 磁盘空间不足
   - 清理磁盘空间
   - 调整水位线设置

3. 节点离线
   - 重启节点
   - 检查网络连接

# 强制分配分片 (谨慎使用)
curl -X POST "localhost:9200/_cluster/reroute" -H "Content-Type: application/json" -d'
{
  "commands": [
    {
      "allocate_empty_primary": {
        "index": "your-index",
        "shard": 0,
        "node": "node-1",
        "accept_data_loss": true
      }
    }
  ]
}'
```

#### **集群状态为黄色**
```bash
# 检查副本分片
curl -X GET "localhost:9200/_cat/shards?v" | grep UNASSIGNED

# 调整副本数
curl -X PUT "localhost:9200/your-index/_settings" -H "Content-Type: application/json" -d'
{
  "index": {
    "number_of_replicas": 1
  }
}'

# 等待分片分配
curl -X GET "localhost:9200/_cluster/health?wait_for_status=green&timeout=30s"
```

### 2. 性能问题

#### **查询性能差**
```bash
# 检查慢查询日志
tail -f /export/logs/elasticsearch/es-cluster-001_index_search_slowlog.log

# 检查查询统计
curl -X GET "localhost:9200/_nodes/stats/indices/search?pretty"

# 优化建议
1. 优化查询语句
   - 使用filter代替query
   - 避免深度分页
   - 使用合适的分析器

2. 调整缓存
   - 增加query cache
   - 调整field data cache

3. 优化映射
   - 禁用不需要的字段
   - 使用合适的数据类型

# 示例优化查询
curl -X GET "localhost:9200/your-index/_search" -H "Content-Type: application/json" -d'
{
  "query": {
    "bool": {
      "filter": [
        {"term": {"status": "active"}},
        {"range": {"timestamp": {"gte": "2024-01-01"}}}
      ]
    }
  },
  "size": 10,
  "from": 0
}'
```

#### **索引性能差**
```bash
# 检查索引统计
curl -X GET "localhost:9200/_nodes/stats/indices/indexing?pretty"

# 检查段合并
curl -X GET "localhost:9200/_cat/segments?v"

# 优化建议
1. 调整刷新间隔
curl -X PUT "localhost:9200/your-index/_settings" -H "Content-Type: application/json" -d'
{
  "index": {
    "refresh_interval": "30s"
  }
}'

2. 批量索引
curl -X POST "localhost:9200/_bulk" -H "Content-Type: application/json" --data-binary @bulk_data.json

3. 临时禁用副本
curl -X PUT "localhost:9200/your-index/_settings" -H "Content-Type: application/json" -d'
{
  "index": {
    "number_of_replicas": 0
  }
}'
```

### 3. 内存问题

#### **堆内存使用率高**
```bash
# 检查JVM状态
curl -X GET "localhost:9200/_nodes/stats/jvm?pretty"

# 检查字段数据缓存
curl -X GET "localhost:9200/_nodes/stats/indices/fielddata?pretty"

# 解决方案
1. 调整堆内存大小
echo "-Xms4g" >> /export/server/elasticsearch/config/jvm.options
echo "-Xmx4g" >> /export/server/elasticsearch/config/jvm.options

2. 清理字段数据缓存
curl -X POST "localhost:9200/_cache/clear?fielddata=true"

3. 调整缓存大小
curl -X PUT "localhost:9200/_cluster/settings" -H "Content-Type: application/json" -d'
{
  "persistent": {
    "indices.fielddata.cache.size": "20%"
  }
}'
```

## 网络问题排查

### 1. 连接问题

#### **网络分区**
```bash
# 检查网络连通性
ping -c 5 ***************
traceroute ***************

# 检查端口连通性
telnet *************** 9092
nc -zv *************** 9092

# 检查防火墙
iptables -L -n
firewall-cmd --list-all

# 检查网络延迟
ping -c 100 *************** | tail -1
```

#### **DNS解析问题**
```bash
# 检查DNS解析
nslookup node1.cluster.local
dig node1.cluster.local

# 检查hosts文件
cat /etc/hosts

# 添加hosts记录
echo "*************** node1.cluster.local" >> /etc/hosts
echo "*************** node2.cluster.local" >> /etc/hosts
echo "*************** node3.cluster.local" >> /etc/hosts
```

### 2. 性能问题

#### **网络带宽瓶颈**
```bash
# 检查网络使用率
iftop -i eth0
nethogs

# 检查网络统计
cat /proc/net/dev
ss -i

# 网络性能测试
iperf3 -s  # 在一台机器上启动服务器
iperf3 -c *************** -t 60  # 在另一台机器上测试
```

## 故障预防

### 1. 监控告警

#### **关键指标监控**
```yaml
# 监控配置示例
alerts:
  - name: HighCPUUsage
    condition: cpu_usage > 80
    duration: 5m
    
  - name: HighMemoryUsage
    condition: memory_usage > 85
    duration: 5m
    
  - name: DiskSpaceLow
    condition: disk_usage > 90
    duration: 1m
    
  - name: NetworkLatencyHigh
    condition: network_latency > 100ms
    duration: 2m
```

### 2. 定期检查

#### **健康检查脚本**
```bash
#!/bin/bash
# health_check.sh

echo "=== 系统健康检查 ==="

# 检查磁盘空间
echo "1. 磁盘空间检查:"
df -h | awk '$5 > 80 {print "WARNING: " $0}'

# 检查内存使用
echo "2. 内存使用检查:"
free -h | awk 'NR==2{printf "Memory Usage: %s/%s (%.2f%%)\n", $3,$2,$3*100/$2 }'

# 检查服务状态
echo "3. 服务状态检查:"
services=("zookeeper" "kafka" "elasticsearch")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✓ $service is running"
    else
        echo "✗ $service is not running"
    fi
done

# 检查端口监听
echo "4. 端口监听检查:"
ports=(2181 9092 9200)
for port in "${ports[@]}"; do
    if netstat -tlnp | grep -q ":$port "; then
        echo "✓ Port $port is listening"
    else
        echo "✗ Port $port is not listening"
    fi
done

# 检查日志错误
echo "5. 日志错误检查:"
find /export/logs -name "*.log" -mtime -1 -exec grep -l "ERROR\|FATAL" {} \; | head -5
```

### 3. 备份策略

#### **自动备份脚本**
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/export/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份配置文件
tar -czf $BACKUP_DIR/configs.tar.gz /export/server/*/config/

# 备份ZooKeeper数据
tar -czf $BACKUP_DIR/zookeeper.tar.gz /export/data/zookeeper/

# 备份Kafka数据 (可选，数据量大)
# tar -czf $BACKUP_DIR/kafka.tar.gz /export/data/kafka/

# Elasticsearch快照
curl -X PUT "localhost:9200/_snapshot/backup_repo/snapshot_$(date +%Y%m%d_%H%M%S)?wait_for_completion=true"

# 清理旧备份
find /export/backup -type d -mtime +7 -exec rm -rf {} \;

echo "Backup completed: $BACKUP_DIR"
```

## 总结

故障排查的关键要点：

1. **系统化方法**: 按照标准流程进行排查
2. **日志分析**: 充分利用日志信息定位问题
3. **监控数据**: 结合监控指标分析问题
4. **经验积累**: 建立故障知识库
5. **预防为主**: 通过监控和检查预防故障
6. **快速恢复**: 制定应急预案和恢复流程

通过系统化的故障排查方法和完善的监控体系，可以快速定位和解决问题，保证系统的稳定运行。
