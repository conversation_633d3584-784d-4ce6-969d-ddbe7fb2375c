# Elasticsearch Log4j2配置文件

status = error

# 日志输出目录
appender.console.type = Console
appender.console.name = console
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = [%d{ISO8601}][%-5p][%-25c{1.}] [%node_name]%marker %m%n

# 主日志文件
appender.rolling.type = RollingFile
appender.rolling.name = rolling
appender.rolling.fileName = ${sys:es.logs.base_path}${sys:file.separator}${sys:es.logs.cluster_name}.log
appender.rolling.layout.type = PatternLayout
appender.rolling.layout.pattern = [%d{ISO8601}][%-5p][%-25c{1.}] [%node_name]%marker %m%n
appender.rolling.filePattern = ${sys:es.logs.base_path}${sys:file.separator}${sys:es.logs.cluster_name}-%d{yyyy-MM-dd}-%i.log.gz
appender.rolling.policies.type = Policies
appender.rolling.policies.time.type = TimeBasedTriggeringPolicy
appender.rolling.policies.time.interval = 1
appender.rolling.policies.time.modulate = true
appender.rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.rolling.policies.size.size = 256MB
appender.rolling.strategy.type = DefaultRolloverStrategy
appender.rolling.strategy.fileIndex = nomax
appender.rolling.strategy.action.type = Delete
appender.rolling.strategy.action.basepath = ${sys:es.logs.base_path}
appender.rolling.strategy.action.condition.type = IfFileName
appender.rolling.strategy.action.condition.glob = ${sys:es.logs.cluster_name}-*
appender.rolling.strategy.action.condition.nested_condition.type = IfAccumulatedFileSize
appender.rolling.strategy.action.condition.nested_condition.exceeds = 2GB

# 慢查询日志
appender.index_search_slowlog_rolling.type = RollingFile
appender.index_search_slowlog_rolling.name = index_search_slowlog_rolling
appender.index_search_slowlog_rolling.fileName = ${sys:es.logs.base_path}${sys:file.separator}${sys:es.logs.cluster_name}_index_search_slowlog.log
appender.index_search_slowlog_rolling.layout.type = PatternLayout
appender.index_search_slowlog_rolling.layout.pattern = [%d{ISO8601}][%-5p][%-25c] [%node_name]%marker %m%n
appender.index_search_slowlog_rolling.filePattern = ${sys:es.logs.base_path}${sys:file.separator}${sys:es.logs.cluster_name}_index_search_slowlog-%d{yyyy-MM-dd}-%i.log.gz
appender.index_search_slowlog_rolling.policies.type = Policies
appender.index_search_slowlog_rolling.policies.time.type = TimeBasedTriggeringPolicy
appender.index_search_slowlog_rolling.policies.time.interval = 1
appender.index_search_slowlog_rolling.policies.time.modulate = true
appender.index_search_slowlog_rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.index_search_slowlog_rolling.policies.size.size = 256MB
appender.index_search_slowlog_rolling.strategy.type = DefaultRolloverStrategy
appender.index_search_slowlog_rolling.strategy.fileIndex = nomax
appender.index_search_slowlog_rolling.strategy.action.type = Delete
appender.index_search_slowlog_rolling.strategy.action.basepath = ${sys:es.logs.base_path}
appender.index_search_slowlog_rolling.strategy.action.condition.type = IfFileName
appender.index_search_slowlog_rolling.strategy.action.condition.glob = ${sys:es.logs.cluster_name}_index_search_slowlog-*
appender.index_search_slowlog_rolling.strategy.action.condition.nested_condition.type = IfAccumulatedFileSize
appender.index_search_slowlog_rolling.strategy.action.condition.nested_condition.exceeds = 2GB

# 慢索引日志
appender.index_indexing_slowlog_rolling.type = RollingFile
appender.index_indexing_slowlog_rolling.name = index_indexing_slowlog_rolling
appender.index_indexing_slowlog_rolling.fileName = ${sys:es.logs.base_path}${sys:file.separator}${sys:es.logs.cluster_name}_index_indexing_slowlog.log
appender.index_indexing_slowlog_rolling.layout.type = PatternLayout
appender.index_indexing_slowlog_rolling.layout.pattern = [%d{ISO8601}][%-5p][%-25c] [%node_name]%marker %m%n
appender.index_indexing_slowlog_rolling.filePattern = ${sys:es.logs.base_path}${sys:file.separator}${sys:es.logs.cluster_name}_index_indexing_slowlog-%d{yyyy-MM-dd}-%i.log.gz
appender.index_indexing_slowlog_rolling.policies.type = Policies
appender.index_indexing_slowlog_rolling.policies.time.type = TimeBasedTriggeringPolicy
appender.index_indexing_slowlog_rolling.policies.time.interval = 1
appender.index_indexing_slowlog_rolling.policies.time.modulate = true
appender.index_indexing_slowlog_rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.index_indexing_slowlog_rolling.policies.size.size = 256MB
appender.index_indexing_slowlog_rolling.strategy.type = DefaultRolloverStrategy
appender.index_indexing_slowlog_rolling.strategy.fileIndex = nomax
appender.index_indexing_slowlog_rolling.strategy.action.type = Delete
appender.index_indexing_slowlog_rolling.strategy.action.basepath = ${sys:es.logs.base_path}
appender.index_indexing_slowlog_rolling.strategy.action.condition.type = IfFileName
appender.index_indexing_slowlog_rolling.strategy.action.condition.glob = ${sys:es.logs.cluster_name}_index_indexing_slowlog-*
appender.index_indexing_slowlog_rolling.strategy.action.condition.nested_condition.type = IfAccumulatedFileSize
appender.index_indexing_slowlog_rolling.strategy.action.condition.nested_condition.exceeds = 2GB

# GC日志
appender.gc.type = RollingFile
appender.gc.name = gc
appender.gc.fileName = ${sys:es.logs.base_path}${sys:file.separator}gc.log
appender.gc.layout.type = PatternLayout
appender.gc.layout.pattern = %m%n
appender.gc.filePattern = ${sys:es.logs.base_path}${sys:file.separator}gc.log.%i
appender.gc.policies.type = Policies
appender.gc.policies.size.type = SizeBasedTriggeringPolicy
appender.gc.policies.size.size = 64MB
appender.gc.strategy.type = DefaultRolloverStrategy
appender.gc.strategy.max = 32

# 根日志配置
rootLogger.level = info
rootLogger.appenderRef.console.ref = console
rootLogger.appenderRef.rolling.ref = rolling

# 特定日志级别配置
logger.action.name = org.elasticsearch.action
logger.action.level = debug

logger.transport.name = org.elasticsearch.transport
logger.transport.level = trace

# 慢查询日志配置
logger.index_search_slowlog_rolling.name = index.search.slowlog
logger.index_search_slowlog_rolling.level = trace
logger.index_search_slowlog_rolling.appenderRef.index_search_slowlog_rolling.ref = index_search_slowlog_rolling
logger.index_search_slowlog_rolling.additivity = false

logger.index_indexing_slowlog.name = index.indexing.slowlog.index
logger.index_indexing_slowlog.level = trace
logger.index_indexing_slowlog.appenderRef.index_indexing_slowlog_rolling.ref = index_indexing_slowlog_rolling
logger.index_indexing_slowlog.additivity = false

# 弃用日志
logger.deprecation.name = org.elasticsearch.deprecation
logger.deprecation.level = warn
