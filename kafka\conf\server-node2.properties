# Kafka 4.0 KRaft模式配置文件 - 节点2
# 192.168.200.102

############################# KRaft Mode Basics #############################

# 进程角色：broker,controller
process.roles=broker,controller

# 节点ID（节点2）
node.id=2

# 控制器仲裁选民（三节点集群）
controller.quorum.voters=1@192.168.200.101:9093,2@192.168.200.102:9093,3@192.168.200.103:9093

############################# Network Settings #############################

# 监听器配置
listeners=SASL_PLAINTEXT://192.168.200.102:9092,CONTROLLER://192.168.200.102:9093
advertised.listeners=SASL_PLAINTEXT://192.168.200.102:9092

# 监听器安全协议映射
listener.security.protocol.map=SASL_PLAINTEXT:SASL_PLAINTEXT,CONTROLLER:PLAINTEXT

# 控制器监听器
controller.listener.names=CONTROLLER

# 集群间通信监听器
inter.broker.listener.name=SASL_PLAINTEXT

############################# Storage Settings #############################

# 日志文件存储目录
log.dirs=/export/data/kafka/logs

# 元数据日志目录
metadata.log.dir=/export/data/kafka/metadata

# 集群ID（所有节点必须相同）
cluster.id=kafka-cluster-001

############################# SASL Authentication Settings #############################

# SASL机制
sasl.enabled.mechanisms=PLAIN

# SASL/PLAIN配置
sasl.mechanism.inter.broker.protocol=PLAIN

# 监听器SASL配置
listener.name.sasl_plaintext.sasl.enabled.mechanisms=PLAIN
listener.name.sasl_plaintext.plain.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required \
    username="admin" \
    password="admin123" \
    user_admin="admin123" \
    user_producer="producer123" \
    user_consumer="consumer123";

############################# Basic Settings #############################

# 默认分区数量
num.partitions=3

# 内部topic的复制因子
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2

# 日志保留时间（小时）
log.retention.hours=168

# 日志段文件大小
log.segment.bytes=1073741824

# 网络线程数量
num.network.threads=3

# IO线程数量
num.io.threads=8

# Socket缓冲区大小
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

# 自动创建topic
auto.create.topics.enable=true

# 删除topic功能
delete.topic.enable=true
