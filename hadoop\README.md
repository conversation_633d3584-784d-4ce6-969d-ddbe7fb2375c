# Hadoop HA集群配置文档

## 概述

本文档描述了基于ZooKeeper的Hadoop 3.x HA（高可用）集群配置，包括HDFS HA和YARN HA。

## 集群架构

### 节点规划
```
192.168.200.101 - NameNode1, ResourceManager1, DataNode, NodeManager, JournalNode
192.168.200.102 - NameNode2, ResourceManager2, DataNode, NodeManager, JournalNode  
192.168.200.103 - DataNode, NodeManager, JournalNode, JobHistoryServer
```

### 服务端口
```
HDFS:
- NameNode RPC: 9000
- NameNode HTTP: 9870
- DataNode: 9864
- JournalNode: 8485

YARN:
- ResourceManager: 8088
- NodeManager: 8042
- JobHistoryServer: 19888

ZooKeeper: 4181
```

## 配置文件说明

### 1. core-site.xml
- 配置默认文件系统为HA集群
- ZooKeeper集群地址配置
- 基础安全和性能参数

### 2. hdfs-site.xml
- NameService和NameNode配置
- JournalNode集群配置
- 自动故障转移配置
- Fencing机制配置

### 3. yarn-site.xml
- ResourceManager HA配置
- NodeManager资源配置
- 日志聚合配置

### 4. mapred-site.xml
- MapReduce运行在YARN上
- JobHistoryServer配置
- 任务资源配置

## 部署步骤

### 1. 前置条件
```bash
# 确保ZooKeeper集群运行正常
# 确保SSH免密登录配置完成
# 确保Java环境已安装
```

### 2. 执行部署脚本
```bash
chmod +x hadoop/setup-hadoop-ha.sh
./hadoop/setup-hadoop-ha.sh
```

### 3. 验证部署
```bash
# 检查集群状态
./hadoop/manage-hadoop-cluster.sh status

# 检查NameNode状态
hdfs haadmin -getServiceState nn1
hdfs haadmin -getServiceState nn2
```

## 集群管理

### 启动集群
```bash
# 启动整个集群
./manage-hadoop-cluster.sh start-all

# 分别启动HDFS和YARN
./manage-hadoop-cluster.sh start-hdfs
./manage-hadoop-cluster.sh start-yarn
```

### 停止集群
```bash
# 停止整个集群
./manage-hadoop-cluster.sh stop-all

# 分别停止YARN和HDFS
./manage-hadoop-cluster.sh stop-yarn
./manage-hadoop-cluster.sh stop-hdfs
```

### 故障转移
```bash
# 自动故障转移（推荐）
# 系统会自动检测故障并切换

# 手动故障转移
./manage-hadoop-cluster.sh failover

# 或使用命令行
hdfs haadmin -failover nn1 nn2
```

### 健康检查
```bash
# 集群健康检查
./manage-hadoop-cluster.sh check-health

# HDFS健康检查
hdfs dfsadmin -report

# YARN节点检查
yarn node -list
```

## Web界面访问

### HDFS
- NameNode1: http://192.168.200.101:9870
- NameNode2: http://192.168.200.102:9870

### YARN
- ResourceManager1: http://192.168.200.101:8088
- ResourceManager2: http://192.168.200.102:8088

### MapReduce
- JobHistoryServer: http://192.168.200.103:19888

## 常用命令

### HDFS命令
```bash
# 查看HDFS状态
hdfs dfsadmin -report

# 查看NameNode状态
hdfs haadmin -getServiceState nn1
hdfs haadmin -getServiceState nn2

# 手动故障转移
hdfs haadmin -failover nn1 nn2

# 进入/退出安全模式
hdfs dfsadmin -safemode enter
hdfs dfsadmin -safemode leave

# 文件操作
hdfs dfs -ls /
hdfs dfs -mkdir /test
hdfs dfs -put localfile /test/
```

### YARN命令
```bash
# 查看节点状态
yarn node -list

# 查看应用程序
yarn application -list

# 查看队列信息
yarn queue -status default

# 杀死应用程序
yarn application -kill application_id
```

## 故障排查

### 1. NameNode无法启动
```bash
# 检查JournalNode是否运行
jps | grep JournalNode

# 检查ZooKeeper连接
echo stat | nc localhost 4181

# 查看NameNode日志
tail -f /export/logs/hadoop/hadoop-root-namenode-*.log
```

### 2. 自动故障转移失败
```bash
# 检查ZKFC进程
jps | grep DFSZKFailoverController

# 重新初始化ZKFC
hdfs zkfc -formatZK

# 查看ZKFC日志
tail -f /export/logs/hadoop/hadoop-root-zkfc-*.log
```

### 3. DataNode无法连接NameNode
```bash
# 检查网络连通性
telnet namenode_ip 9000

# 检查DataNode日志
tail -f /export/logs/hadoop/hadoop-root-datanode-*.log

# 重启DataNode
hdfs --daemon stop datanode
hdfs --daemon start datanode
```

## 性能调优

### 1. JVM参数调优
- NameNode: -Xmx2g -Xms2g -XX:+UseG1GC
- DataNode: -Xmx1g -Xms1g -XX:+UseG1GC
- ResourceManager: -Xmx2g -Xms2g -XX:+UseG1GC

### 2. HDFS参数调优
- dfs.blocksize: 134217728 (128MB)
- dfs.replication: 3
- dfs.namenode.handler.count: 20
- dfs.datanode.handler.count: 10

### 3. YARN参数调优
- yarn.nodemanager.resource.memory-mb: 4096
- yarn.nodemanager.resource.cpu-vcores: 4
- yarn.scheduler.maximum-allocation-mb: 4096

## 备份策略

### 1. NameNode元数据备份
```bash
# 定期备份NameNode目录
tar -czf namenode-backup-$(date +%Y%m%d).tar.gz /export/data/hadoop/namenode/

# 备份fsimage
hdfs dfsadmin -fetchImage /backup/fsimage-$(date +%Y%m%d)
```

### 2. 配置文件备份
```bash
# 备份配置文件
tar -czf hadoop-conf-backup-$(date +%Y%m%d).tar.gz /export/server/hadoop/etc/hadoop/
```

## 监控建议

### 1. 关键指标监控
- NameNode堆内存使用率
- DataNode磁盘使用率
- HDFS块丢失数量
- YARN资源使用率
- 网络IO和磁盘IO

### 2. 日志监控
- 监控ERROR级别日志
- 关注GC日志
- 监控慢查询日志

## 安全建议

### 1. 网络安全
- 配置防火墙规则
- 限制管理端口访问
- 使用VPN访问Web界面

### 2. 认证授权
- 启用Kerberos认证（生产环境）
- 配置HDFS权限
- 设置YARN队列权限

## 注意事项

1. **确保时钟同步**: 所有节点时间必须同步
2. **磁盘空间**: 保持足够的磁盘空间，建议使用率不超过80%
3. **网络稳定**: 确保节点间网络稳定，延迟低于10ms
4. **资源规划**: 合理分配内存和CPU资源
5. **定期维护**: 定期清理日志文件和临时文件
