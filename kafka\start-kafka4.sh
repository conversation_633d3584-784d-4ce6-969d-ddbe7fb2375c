#!/bin/bash

# Kafka 4.0 KRaft模式启动脚本（带SASL/PLAIN认证）

# 配置集群ID（所有节点必须使用相同的ID）
DEFAULT_CLUSTER_ID="kafka-cluster-001"

echo "=== 启动Kafka 4.0 (KRaft + SASL/PLAIN) ==="

# 设置JDK 17环境
export JAVA_HOME=/export/tools/jdk17
export PATH=$JAVA_HOME/bin:$PATH

# 验证Java 17安装
if [ ! -x "$JAVA_HOME/bin/java" ]; then
    echo "错误: JDK 17不存在或不可执行: $JAVA_HOME/bin/java"
    echo "请确认JDK 17已正确安装到 /export/tools/jdk17"
    exit 1
fi

echo "Java环境: $JAVA_HOME"
echo "Java版本: $($JAVA_HOME/bin/java -version 2>&1 | head -n 1)"

# 确保使用正确的Java版本
java_version_check=$($JAVA_HOME/bin/java -version 2>&1 | head -n 1)
if [[ $java_version_check == *"17."* ]]; then
    echo "✓ 使用JDK 17"
else
    echo "错误: 当前Java版本不是17"
    echo "当前版本: $java_version_check"
    exit 1
fi

# 创建必要目录
mkdir -p /export/logs/kafka
mkdir -p /export/data/kafka/logs
mkdir -p /export/data/kafka/metadata

echo "✓ 目录创建完成"

# 设置JVM参数
export KAFKA_HEAP_OPTS="-Xmx2G -Xms2G"
export KAFKA_GC_LOG_OPTS="-Xlog:gc*:/export/logs/kafka/kafkaServer-gc.log:time,tags:filecount=10,filesize=100M"
export KAFKA_JVM_PERFORMANCE_OPTS="-server -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35 -XX:+ExplicitGCInvokesConcurrent -Djava.awt.headless=true --add-opens=java.base/sun.nio.ch=ALL-UNNAMED"

# 检查配置文件
if [ ! -f "/export/server/kafka/config/server.properties" ]; then
    echo "✗ 错误: server.properties不存在"
    exit 1
fi

echo "✓ 配置文件检查完成"

# 检查KRaft存储是否已格式化
if [ ! -f "/export/data/kafka/metadata/meta.properties" ]; then
    echo ""
    echo "首次启动，需要格式化KRaft存储..."

    # 使用指定的集群ID（不使用随机ID）
    CLUSTER_ID="${CLUSTER_ID:-$DEFAULT_CLUSTER_ID}"
    echo "使用指定集群ID: $CLUSTER_ID"

    echo ""
    echo "⚠ 重要提醒："
    echo "  集群中的所有节点必须使用相同的集群ID！"
    echo "  当前使用的集群ID: $CLUSTER_ID"
    echo "  如果其他节点已经格式化，请确保使用相同的集群ID"
    echo ""

    read -p "确认使用此集群ID继续格式化？(y/n): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "格式化已取消"
        echo ""
        echo "如需使用不同的集群ID，请修改脚本中的CLUSTER_ID变量"
        echo "或手动执行格式化命令："
        echo "  cd /export/server/kafka"
        echo "  bin/kafka-storage.sh format --config config/server.properties --cluster-id your-cluster-id"
        exit 1
    fi

    cd /export/server/kafka

    # 格式化存储
    echo ""
    echo "开始格式化存储..."
    echo "执行命令: bin/kafka-storage.sh format --config config/server.properties --cluster-id $CLUSTER_ID"

    bin/kafka-storage.sh format --config config/server.properties --cluster-id "$CLUSTER_ID"

    if [ $? -eq 0 ]; then
        echo "✓ KRaft存储格式化完成"
        echo "✓ 集群ID: $CLUSTER_ID"

        # 保存集群ID到文件供参考
        echo "$CLUSTER_ID" > /tmp/kafka-cluster-id.txt
        echo "✓ 集群ID已保存到 /tmp/kafka-cluster-id.txt"
    else
        echo "✗ KRaft存储格式化失败"
        echo "请检查配置文件和权限"
        exit 1
    fi
else
    echo "✓ KRaft存储已格式化"

    # 显示当前集群ID
    if [ -f "/export/data/kafka/metadata/meta.properties" ]; then
        current_cluster_id=$(grep "cluster.id=" /export/data/kafka/metadata/meta.properties | cut -d'=' -f2)
        echo "✓ 当前集群ID: $current_cluster_id"
    fi
fi

# 启动Kafka
echo ""
echo "启动Kafka KRaft模式..."
cd /export/server/kafka

# 后台启动
nohup bin/kafka-server-start.sh config/server.properties > /export/logs/kafka/kafka.log 2>&1 &
kafka_pid=$!

echo "Kafka启动中，PID: $kafka_pid"
echo "日志文件: /export/logs/kafka/kafka.log"

# 等待启动
echo "等待Kafka启动..."
sleep 15

# 检查进程
if ps -p $kafka_pid > /dev/null 2>&1; then
    echo "✓ Kafka进程运行中 (PID: $kafka_pid)"
else
    echo "✗ Kafka启动失败"
    echo "请检查日志: tail -50 /export/logs/kafka/kafka.log"
    exit 1
fi

# 检查端口
if netstat -tlnp 2>/dev/null | grep -q ":9092"; then
    echo "✓ Kafka端口9092正在监听"
else
    echo "⚠ Kafka端口9092未监听，可能还在启动中"
fi

if netstat -tlnp 2>/dev/null | grep -q ":9093"; then
    echo "✓ Controller端口9093正在监听"
else
    echo "⚠ Controller端口9093未监听，可能还在启动中"
fi

echo ""
echo "=== Kafka 4.0启动完成 ==="
echo ""
echo "SASL/PLAIN认证信息:"
echo "  管理员: admin / admin123"
echo "  生产者: producer / producer123"
echo "  消费者: consumer / consumer123"
echo ""
echo "连接示例:"
echo "  服务器地址: 192.168.200.101:9092"
echo "  安全协议: SASL_PLAINTEXT"
echo "  SASL机制: PLAIN"
echo ""
echo "测试命令:"
echo "  # 创建客户端配置文件"
echo "  echo 'security.protocol=SASL_PLAINTEXT' > client.properties"
echo "  echo 'sasl.mechanism=PLAIN' >> client.properties"
echo "  echo 'sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username=\"admin\" password=\"admin123\";' >> client.properties"
echo ""
echo "  # 创建topic"
echo "  bin/kafka-topics.sh --create --topic test --bootstrap-server localhost:9092 --command-config client.properties"
echo ""
echo "  # 查看topic"
echo "  bin/kafka-topics.sh --list --bootstrap-server localhost:9092 --command-config client.properties"
