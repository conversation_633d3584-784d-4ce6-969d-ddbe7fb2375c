#!/bin/bash

# ZooKeeper Environment Configuration
# For ZooKeeper 3.8.4 with SASL Authentication

# Java环境变量
export JAVA_HOME=/export/tools/jdk8

# ZooKeeper路径配置
ZOOBINDIR="${ZOOBINDIR:-/usr/bin}"
ZOOKEEPER_PREFIX="${ZOOBINDIR}/.."

# ZooKeeper安装目录（自定义路径）
export ZOOKEEPER_HOME=/export/server/zookeeper

#check to see if the conf dir is given as an optional argument
if [ $# -gt 1 ]
then
    if [ "--config" = "$1" ]
          then
              shift
              confdir=$1
              shift
              ZOOCFGDIR=$confdir
    fi
fi

if [ "x$ZOOCFGDIR" = "x" ]
then
  if [ -e "${ZOOKEEPER_PREFIX}/conf" ]; then
    ZOOCFGDIR="$ZOOBINDIR/../conf"
  else
    ZOOCFGDIR="$ZOOBINDIR/../etc/zookeeper"
  fi
fi

# 如果使用自定义安装路径，优先使用自定义配置目录
if [ -d "$ZOOKEEPER_HOME/conf" ]; then
    ZOOCFGDIR="$ZOOKEEPER_HOME/conf"
fi

if [ -f "${ZOOCFGDIR}/zookeeper-env.sh" ]; then
  . "${ZOOCFGDIR}/zookeeper-env.sh"
fi

if [ "x$ZOOCFG" = "x" ]
then
    ZOOCFG="zoo.cfg"
fi

ZOOCFG="$ZOOCFGDIR/$ZOOCFG"

if [ -f "$ZOOCFGDIR/java.env" ]
then
    . "$ZOOCFGDIR/java.env"
fi

# ZooKeeper日志目录配置
if [ "x${ZOO_LOG_DIR}" = "x" ]
then
    ZOO_LOG_DIR="/export/logs/zookeeper"
fi

# ZooKeeper日志级别
export ZOO_LOG4J_PROP=INFO,ROLLINGFILE

# Java可执行文件检查
if [[ -n "$JAVA_HOME" ]] && [[ -x "$JAVA_HOME/bin/java" ]];  then
    JAVA="$JAVA_HOME/bin/java"
elif type -p java; then
    JAVA=java
else
    echo "Error: JAVA_HOME is not set and java could not be found in PATH." 1>&2
    exit 1
fi

#add the zoocfg dir to classpath
CLASSPATH="$ZOOCFGDIR:$CLASSPATH"

for i in "$ZOOBINDIR"/../zookeeper-server/src/main/resources/lib/*.jar
do
    CLASSPATH="$i:$CLASSPATH"
done

#make it work in the binary package
#(use array for LIBPATH to account for spaces within wildcard expansion)
if ls "${ZOOKEEPER_PREFIX}"/share/zookeeper/zookeeper-*.jar > /dev/null 2>&1; then
  LIBPATH=("${ZOOKEEPER_PREFIX}"/share/zookeeper/*.jar)
else
  #release tarball format
  for i in "$ZOOBINDIR"/../zookeeper-*.jar
  do
    CLASSPATH="$i:$CLASSPATH"
  done
  LIBPATH=("${ZOOBINDIR}"/../lib/*.jar)
fi

# 如果使用自定义安装路径，添加自定义路径的jar包
if [ -d "$ZOOKEEPER_HOME/lib" ]; then
    for i in "$ZOOKEEPER_HOME"/lib/*.jar
    do
        CLASSPATH="$i:$CLASSPATH"
    done
fi

if [ -f "$ZOOKEEPER_HOME/zookeeper-"*.jar ]; then
    for i in "$ZOOKEEPER_HOME"/zookeeper-*.jar
    do
        CLASSPATH="$i:$CLASSPATH"
    done
fi

for i in "${LIBPATH[@]}"
do
    CLASSPATH="$i:$CLASSPATH"
done

#make it work for developers
for d in "$ZOOBINDIR"/../build/lib/*.jar
do
   CLASSPATH="$d:$CLASSPATH"
done

for d in "$ZOOBINDIR"/../zookeeper-server/target/lib/*.jar
do
   CLASSPATH="$d:$CLASSPATH"
done

#make it work for developers
CLASSPATH="$ZOOBINDIR/../build/classes:$CLASSPATH"

#make it work for developers
CLASSPATH="$ZOOBINDIR/../zookeeper-server/target/classes:$CLASSPATH"

case "`uname`" in
    CYGWIN*|MINGW*) cygwin=true ;;
    *) cygwin=false ;;
esac

if $cygwin
then
    CLASSPATH=`cygpath -wp "$CLASSPATH"`
fi

#echo "CLASSPATH=$CLASSPATH"

# default heap for zookeeper server
ZK_SERVER_HEAP="${ZK_SERVER_HEAP:-1000}"

# default heap for zookeeper client
ZK_CLIENT_HEAP="${ZK_CLIENT_HEAP:-256}"

# JVM参数设置
JVMFLAGS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# SASL认证相关配置
export SERVER_JVMFLAGS="-Xmx${ZK_SERVER_HEAP}m $JVMFLAGS -Djava.security.auth.login.config=$ZOOCFGDIR/jaas.conf"
export CLIENT_JVMFLAGS="-Xmx${ZK_CLIENT_HEAP}m $JVMFLAGS -Djava.security.auth.login.config=$ZOOCFGDIR/jaas.conf"

# Kerberos相关配置（如果使用Kerberos）
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Djava.security.krb5.conf=/etc/krb5.conf"
export CLIENT_JVMFLAGS="$CLIENT_JVMFLAGS -Djava.security.krb5.conf=/etc/krb5.conf"

# 启用SASL认证（3.8.4版本）
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider"
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.sessionRequireClientSASLAuth=true"

# 审计和安全日志
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.audit.enable=true"
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.audit.destination=FILE"
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.audit.file=$ZOO_LOG_DIR/audit.log"

# 网络相关配置
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.serverCnxnFactory=org.apache.zookeeper.server.NettyServerCnxnFactory"

# 安全相关配置
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.admin.enableServer=true"
export SERVER_JVMFLAGS="$SERVER_JVMFLAGS -Dzookeeper.admin.serverPort=8080"

# 创建必要的目录
mkdir -p $ZOO_LOG_DIR
mkdir -p /export/data/zookeeper

# 设置权限
chmod 755 $ZOO_LOG_DIR
chmod 755 /export/data/zookeeper
