<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Hadoop MapReduce配置 -->
    
    <!-- MapReduce框架 -->
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
        <description>MapReduce运行在YARN上</description>
    </property>
    
    <!-- JobHistory服务器配置 -->
    <property>
        <name>mapreduce.jobhistory.address</name>
        <value>***************:10020</value>
        <description>JobHistory服务器地址</description>
    </property>
    
    <property>
        <name>mapreduce.jobhistory.webapp.address</name>
        <value>***************:19888</value>
        <description>JobHistory Web地址</description>
    </property>
    
    <!-- Map任务配置 -->
    <property>
        <name>mapreduce.map.memory.mb</name>
        <value>1024</value>
        <description>Map任务内存</description>
    </property>
    
    <property>
        <name>mapreduce.map.java.opts</name>
        <value>-Xmx819m</value>
        <description>Map任务JVM参数</description>
    </property>
    
    <!-- Reduce任务配置 -->
    <property>
        <name>mapreduce.reduce.memory.mb</name>
        <value>1024</value>
        <description>Reduce任务内存</description>
    </property>
    
    <property>
        <name>mapreduce.reduce.java.opts</name>
        <value>-Xmx819m</value>
        <description>Reduce任务JVM参数</description>
    </property>
    
    <!-- ApplicationMaster配置 -->
    <property>
        <name>yarn.app.mapreduce.am.resource.mb</name>
        <value>1024</value>
        <description>ApplicationMaster内存</description>
    </property>
    
    <property>
        <name>yarn.app.mapreduce.am.command-opts</name>
        <value>-Xmx819m</value>
        <description>ApplicationMaster JVM参数</description>
    </property>
    
    <!-- 任务并发配置 -->
    <property>
        <name>mapreduce.job.maps</name>
        <value>2</value>
        <description>默认Map任务数</description>
    </property>
    
    <property>
        <name>mapreduce.job.reduces</name>
        <value>1</value>
        <description>默认Reduce任务数</description>
    </property>
    
    <!-- 压缩配置 -->
    <property>
        <name>mapreduce.map.output.compress</name>
        <value>true</value>
        <description>启用Map输出压缩</description>
    </property>
    
    <property>
        <name>mapreduce.map.output.compress.codec</name>
        <value>org.apache.hadoop.io.compress.SnappyCodec</value>
        <description>Map输出压缩编解码器</description>
    </property>
    
    <!-- 历史日志配置 -->
    <property>
        <name>mapreduce.jobhistory.intermediate-done-dir</name>
        <value>/export/data/hadoop/mr-history/tmp</value>
        <description>作业历史中间目录</description>
    </property>
    
    <property>
        <name>mapreduce.jobhistory.done-dir</name>
        <value>/export/data/hadoop/mr-history/done</value>
        <description>作业历史完成目录</description>
    </property>
</configuration>
