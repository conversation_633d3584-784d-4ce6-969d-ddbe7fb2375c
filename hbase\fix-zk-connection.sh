#!/bin/bash

# HBase ZooKeeper连接问题修复脚本

echo "=== HBase ZooKeeper连接修复 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo "1. 检查ZooKeeper集群状态..."

# 检查ZooKeeper是否运行
zk_running=0
for server in 192.168.200.101 192.168.200.102 192.168.200.103; do
    if echo ruok | nc -w3 $server 4181 2>/dev/null | grep -q imok; then
        echo -e "  ${GREEN}✓${NC} ZooKeeper $server:4181 运行正常"
        ((zk_running++))
    else
        echo -e "  ${RED}✗${NC} ZooKeeper $server:4181 连接失败"
    fi
done

if [ $zk_running -eq 0 ]; then
    echo -e "${RED}错误: ZooKeeper集群未运行，正在尝试启动...${NC}"
    
    # 尝试启动ZooKeeper
    if [ -f "zookeeper/start-zookeeper.sh" ]; then
        echo "  启动ZooKeeper集群..."
        ./zookeeper/start-zookeeper.sh
        
        # 等待启动
        echo "  等待ZooKeeper启动..."
        sleep 15
        
        # 重新检查
        zk_running=0
        for server in 192.168.200.101 192.168.200.102 192.168.200.103; do
            if echo ruok | nc -w3 $server 4181 2>/dev/null | grep -q imok; then
                ((zk_running++))
            fi
        done
        
        if [ $zk_running -gt 0 ]; then
            echo -e "  ${GREEN}✓${NC} ZooKeeper集群启动成功 ($zk_running/3 节点)"
        else
            echo -e "  ${RED}✗${NC} ZooKeeper集群启动失败"
            echo "  请手动检查ZooKeeper配置和日志"
            exit 1
        fi
    else
        echo -e "  ${RED}✗${NC} 找不到ZooKeeper启动脚本"
        echo "  请手动启动ZooKeeper集群"
        exit 1
    fi
fi

echo ""
echo "2. 更新HBase配置..."

# 确保配置文件复制到正确位置
if [ -d "/export/server/hbase/hbase/conf" ]; then
    echo "  复制配置文件到HBase安装目录..."
    cp hbase/conf/* /export/server/hbase/hbase/conf/
    echo -e "  ${GREEN}✓${NC} 配置文件更新完成"
else
    echo -e "  ${RED}✗${NC} HBase配置目录不存在"
    exit 1
fi

echo ""
echo "3. 验证关键配置..."

# 检查分布式模式配置
if grep -q "hbase.cluster.distributed.*true" /export/server/hbase/hbase/conf/hbase-site.xml; then
    echo -e "  ${GREEN}✓${NC} 分布式模式已启用"
else
    echo -e "  ${YELLOW}⚠${NC} 分布式模式配置可能有问题"
fi

# 检查ZooKeeper配置
if grep -q "192.168.200.101,192.168.200.102,192.168.200.103" /export/server/hbase/hbase/conf/hbase-site.xml; then
    echo -e "  ${GREEN}✓${NC} ZooKeeper集群配置正确"
else
    echo -e "  ${RED}✗${NC} ZooKeeper集群配置错误"
fi

# 检查SASL配置
if [ -f "/export/server/hbase/hbase/conf/jaas.conf" ]; then
    echo -e "  ${GREEN}✓${NC} SASL认证配置文件存在"
else
    echo -e "  ${RED}✗${NC} SASL认证配置文件缺失"
fi

echo ""
echo "4. 清理旧的HBase进程..."

# 停止可能运行的HBase进程
echo "  停止现有HBase进程..."
pkill -f HMaster 2>/dev/null
pkill -f HRegionServer 2>/dev/null
sleep 5

# 清理PID文件
if [ -d "/export/server/hbase/hbase/pids" ]; then
    rm -f /export/server/hbase/hbase/pids/*.pid
fi

echo ""
echo "5. 重新启动HBase..."

# 设置环境变量
export JAVA_HOME=/export/tools/jdk8
export HBASE_HOME=/export/server/hbase/hbase
export HBASE_CONF_DIR=/export/server/hbase/hbase/conf

# 启动HBase
echo "  启动HBase Master..."
/export/server/hbase/hbase/bin/hbase-daemon.sh start master

# 等待启动
echo "  等待HBase启动..."
sleep 15

echo ""
echo "6. 验证启动状态..."

# 检查HBase进程
if ps aux | grep -q "[H]Master"; then
    echo -e "  ${GREEN}✓${NC} HBase Master进程运行中"
    
    # 检查端口
    if netstat -tlnp 2>/dev/null | grep -q ":16010"; then
        echo -e "  ${GREEN}✓${NC} HBase Web UI端口16010已监听"
    fi
    
    if netstat -tlnp 2>/dev/null | grep -q ":16000"; then
        echo -e "  ${GREEN}✓${NC} HBase Master端口16000已监听"
    fi
else
    echo -e "  ${RED}✗${NC} HBase Master进程未运行"
    echo ""
    echo "  检查HBase日志:"
    echo "  tail -50 /export/logs/hbase/hbase-*.log"
    exit 1
fi

echo ""
echo "7. 测试HBase连接..."

# 测试HBase Shell连接
echo "  测试HBase Shell连接..."
timeout 10 /export/server/hbase/hbase/bin/hbase shell <<< "list" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "  ${GREEN}✓${NC} HBase Shell连接成功"
else
    echo -e "  ${YELLOW}⚠${NC} HBase Shell连接测试超时或失败"
    echo "  这可能是正常的，因为HBase仍在初始化中"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "HBase服务信息:"
echo "  Web UI: http://192.168.200.101:16010"
echo "  Master: 192.168.200.101:16000"
echo "  Shell: /export/server/hbase/hbase/bin/hbase shell"
echo ""
echo "如果仍有问题，请检查:"
echo "  1. HBase日志: tail -f /export/logs/hbase/hbase-*.log"
echo "  2. ZooKeeper日志: tail -f /export/logs/zookeeper/zookeeper.log"
echo "  3. 运行诊断: ./hbase/diagnose-zk-connection.sh"
