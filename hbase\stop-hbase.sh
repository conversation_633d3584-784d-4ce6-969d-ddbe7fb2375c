#!/bin/bash

# HBase停止脚本

echo "=== 停止HBase集群 ==="

# 设置环境变量
source /export/server/hbase/conf/hbase-env.sh

# 单机模式 - 停止Master会自动停止RegionServer
echo "1. 单机模式 - 准备停止HBase..."

# 集群模式RegionServer停止（注释）
# echo "1. 停止RegionServers..."
# su - appuser -c "/export/server/hbase/bin/hbase-daemons.sh stop regionserver"
#
# # 等待RegionServers停止
# echo "等待RegionServers停止..."
# sleep 5

# 停止HBase（单机模式）
echo ""
echo "2. 停止HBase..."
su - appuser -c "/export/server/hbase/bin/stop-hbase.sh"

# 等待Master停止
echo "等待Master停止..."
sleep 5

# 检查进程是否完全停止
echo ""
echo "3. 检查进程状态..."
hbase_processes=$(ps aux | grep -E "(HMaster|HRegionServer)" | grep -v grep)
if [ -z "$hbase_processes" ]; then
    echo "✓ 所有HBase进程已停止"
else
    echo "⚠ 仍有HBase进程运行:"
    echo "$hbase_processes"
    
    echo ""
    echo "强制终止残留进程..."
    pkill -f HMaster
    pkill -f HRegionServer
    sleep 3
    
    remaining=$(ps aux | grep -E "(HMaster|HRegionServer)" | grep -v grep)
    if [ -z "$remaining" ]; then
        echo "✓ 所有进程已强制终止"
    else
        echo "✗ 仍有进程无法终止，请手动处理"
    fi
fi

echo ""
echo "=== HBase停止完成 ==="
