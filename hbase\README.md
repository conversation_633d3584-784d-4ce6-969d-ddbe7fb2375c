# HBase单机配置 - 适配ZooKeeper SASL认证

## 概述

本配置适用于HBase单机模式与启用SASL认证的ZooKeeper 3.8.4集群集成。

## 配置信息

- **运行模式**: 单机模式（Standalone）
- **ZooKeeper集群**: ***************:4181, ***************:4181, ***************:4181
- **HBase Master**: localhost:16000
- **RegionServer**: localhost（与Master同进程）
- **数据存储**: 本地文件系统 /export/data/hbase

## 配置文件说明

### 1. jaas.conf
- HBase各组件连接ZooKeeper的SASL认证配置
- 使用与ZooKeeper相同的客户端认证信息

### 2. hbase-site.xml
- HBase主配置文件
- 包含ZooKeeper连接和SASL认证配置
- 集群和性能优化配置

### 3. hbase-env.sh
- HBase环境变量配置
- JVM参数和SASL认证设置
- 目录和权限配置

### 4. regionservers
- RegionServer节点列表

### 5. log4j.properties
- 日志配置，包含SASL认证相关日志

## 部署步骤

### 1. 前置条件检查
```bash
# 检查ZooKeeper集群状态
echo ruok | nc *************** 4181
echo ruok | nc *************** 4181  
echo ruok | nc *************** 4181

# 检查HDFS状态
hadoop fs -ls /
```

### 2. 复制配置文件
```bash
# 将配置文件复制到HBase安装目录
cp hbase/conf/* /export/server/hbase/conf/

# 设置执行权限
chmod +x /export/server/hbase/conf/hbase-env.sh
chmod +x hbase/start-hbase.sh
chmod +x hbase/stop-hbase.sh
```

### 3. 创建必要目录
```bash
# 创建本地目录（单机模式）
mkdir -p /export/logs/hbase
mkdir -p /export/data/hbase
mkdir -p /export/server/hbase/pids

# 设置权限
chown -R appuser:appuser /export/logs/hbase
chown -R appuser:appuser /export/data/hbase
chown -R appuser:appuser /export/server/hbase

# 集群模式HDFS目录创建（注释）
# hadoop fs -mkdir -p /hbase
# hadoop fs -chown hbase:hbase /hbase
```

### 4. 启动HBase（单机模式）
```bash
# 使用启动脚本
./hbase/start-hbase.sh

# 或手动启动（单机模式）
/export/server/hbase/bin/start-hbase.sh

# 集群模式启动（注释）
# /export/server/hbase/bin/start-hbase.sh
```

### 5. 验证启动状态
```bash
# 检查进程
ps aux | grep -E "(HMaster|HRegionServer)"

# 检查Web UI
curl -s http://***************:16010 | grep -i hbase

# 使用HBase Shell测试
/export/server/hbase/bin/hbase shell
```

## 测试SASL认证

### 1. 检查ZooKeeper连接日志
```bash
grep -i "zookeeper.*sasl\|sasl.*zookeeper" /export/logs/hbase/hbase-*.log
```

### 2. 验证认证成功
成功的SASL认证日志应包含：
- `SASL authentication successful`
- `Client successfully logged in`
- `ZooKeeper connection established`

## 故障排查

### 1. 常见问题
- **ZooKeeper连接失败**: 检查SASL认证配置和网络连通性
- **HDFS连接问题**: 确认HDFS服务正常运行
- **权限问题**: 检查目录权限和用户配置

### 2. 日志位置
- HBase日志: `/export/logs/hbase/`
- ZooKeeper日志: `/export/logs/zookeeper/`
- HDFS日志: `/export/logs/hadoop/`

### 3. 调试命令
```bash
# 检查ZooKeeper SASL状态
echo stat | nc *************** 4181

# 测试HBase Shell连接
echo "list" | /export/server/hbase/bin/hbase shell

# 检查HBase Master状态
curl http://***************:16010/master-status
```

## 停止服务
```bash
# 使用停止脚本
./hbase/stop-hbase.sh

# 或手动停止
/export/server/hbase/bin/stop-hbase.sh
```

## 注意事项

1. **SASL认证**: 确保jaas.conf中的用户名密码与ZooKeeper配置一致
2. **网络配置**: 确保所有节点间网络互通
3. **时间同步**: 所有节点时间必须同步
4. **资源配置**: 根据实际环境调整JVM内存配置
5. **安全考虑**: 生产环境中应使用更强的密码和加密
