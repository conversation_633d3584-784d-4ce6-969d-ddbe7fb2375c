#!/bin/bash

# Kafka集群状态检查脚本

echo "=== Kafka集群状态检查 ==="

# 设置变量
KAFKA_HOME="/export/server/kafka"
BOOTSTRAP_SERVERS="192.168.200.101:9092,192.168.200.102:9092,192.168.200.103:9092"
NODES=("192.168.200.101" "192.168.200.102" "192.168.200.103")

# 创建临时客户端配置
TEMP_CONFIG="/tmp/kafka-admin-check.properties"
cat > $TEMP_CONFIG << EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";
EOF

cd $KAFKA_HOME

echo ""
echo "1. 检查节点连通性..."
for node in "${NODES[@]}"; do
    echo -n "  节点 $node:9092 - "
    if timeout 3 bash -c "</dev/tcp/$node/9092" 2>/dev/null; then
        echo "✓ 可连接"
    else
        echo "✗ 无法连接"
    fi
    
    echo -n "  节点 $node:9093 - "
    if timeout 3 bash -c "</dev/tcp/$node/9093" 2>/dev/null; then
        echo "✓ 可连接"
    else
        echo "✗ 无法连接"
    fi
done

echo ""
echo "2. 检查进程和端口状态..."
for node in "${NODES[@]}"; do
    echo "  节点 $node:"

    # 检查进程
    if ssh -o ConnectTimeout=3 $node "pgrep -f kafka.Kafka" >/dev/null 2>&1; then
        echo "    ✓ Kafka进程运行中"

        # 检查端口监听
        broker_port=$(ssh -o ConnectTimeout=3 $node "netstat -tlnp 2>/dev/null | grep :9092" 2>/dev/null)
        controller_port=$(ssh -o ConnectTimeout=3 $node "netstat -tlnp 2>/dev/null | grep :9093" 2>/dev/null)

        if [ -n "$broker_port" ]; then
            echo "    ✓ 9092端口正在监听"
        else
            echo "    ✗ 9092端口未监听"
        fi

        if [ -n "$controller_port" ]; then
            echo "    ✓ 9093端口正在监听"
        else
            echo "    ✗ 9093端口未监听"
        fi

        # 检查最近的错误日志
        recent_errors=$(ssh -o ConnectTimeout=3 $node "tail -20 /export/logs/kafka/kafka.log 2>/dev/null | grep -i error | tail -2" 2>/dev/null)
        if [ -n "$recent_errors" ]; then
            echo "    ⚠ 最近错误:"
            echo "$recent_errors" | sed 's/^/      /'
        fi

    else
        echo "    ⚠ 无法检查进程状态（可能需要SSH配置）"
    fi
done

echo ""
echo "3. 检查集群基本信息..."
echo "  获取broker信息..."
if bin/kafka-broker-api-versions.sh \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --command-config $TEMP_CONFIG >/dev/null 2>&1; then
    echo "  ✓ 集群可访问"
    
    # 获取broker数量
    BROKER_COUNT=$(bin/kafka-broker-api-versions.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $TEMP_CONFIG 2>/dev/null | \
        grep "^[0-9]" | wc -l)
    echo "  ✓ 活跃broker数量: $BROKER_COUNT"
else
    echo "  ✗ 集群无法访问"
    rm -f $TEMP_CONFIG
    exit 1
fi

echo ""
echo "4. 检查Topic状态..."
TOPIC_COUNT=$(bin/kafka-topics.sh --list \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --command-config $TEMP_CONFIG 2>/dev/null | wc -l)
echo "  ✓ Topic总数: $TOPIC_COUNT"

# 检查是否有不可用分区
UNAVAILABLE=$(bin/kafka-topics.sh --describe \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --unavailable-partitions \
    --command-config $TEMP_CONFIG 2>/dev/null)

if [ -z "$UNAVAILABLE" ]; then
    echo "  ✓ 所有分区都可用"
else
    echo "  ⚠ 发现不可用分区:"
    echo "$UNAVAILABLE"
fi

# 检查副本不同步情况
UNDER_REPLICATED=$(bin/kafka-topics.sh --describe \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --under-replicated-partitions \
    --command-config $TEMP_CONFIG 2>/dev/null)

if [ -z "$UNDER_REPLICATED" ]; then
    echo "  ✓ 所有副本都已同步"
else
    echo "  ⚠ 发现副本不同步:"
    echo "$UNDER_REPLICATED"
fi

echo ""
echo "5. 检查消费者组..."
CONSUMER_GROUPS=$(bin/kafka-consumer-groups.sh --list \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --command-config $TEMP_CONFIG 2>/dev/null | wc -l)
echo "  ✓ 消费者组数量: $CONSUMER_GROUPS"

echo ""
echo "6. 性能测试..."
echo "  创建测试topic..."
TEST_TOPIC="cluster-health-test-$(date +%s)"

if bin/kafka-topics.sh --create \
    --topic $TEST_TOPIC \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --partitions 3 \
    --replication-factor $(($BROKER_COUNT > 3 ? 3 : $BROKER_COUNT)) \
    --command-config $TEMP_CONFIG >/dev/null 2>&1; then
    echo "  ✓ 测试topic创建成功"
    
    # 发送测试消息
    echo "test message $(date)" | bin/kafka-console-producer.sh \
        --topic $TEST_TOPIC \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --producer.config $TEMP_CONFIG >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "  ✓ 消息发送成功"
    else
        echo "  ✗ 消息发送失败"
    fi
    
    # 清理测试topic
    bin/kafka-topics.sh --delete \
        --topic $TEST_TOPIC \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $TEMP_CONFIG >/dev/null 2>&1
    echo "  ✓ 测试topic已清理"
else
    echo "  ✗ 测试topic创建失败"
fi

echo ""
echo "7. 磁盘使用情况..."
for node in "${NODES[@]}"; do
    echo "  节点 $node:"
    if ssh -o ConnectTimeout=3 $node "df -h /export/data/kafka 2>/dev/null" 2>/dev/null; then
        echo "    ✓ 磁盘使用情况已显示"
    else
        echo "    ⚠ 无法获取磁盘信息"
    fi
done

echo ""
echo "8. 最近错误日志..."
for node in "${NODES[@]}"; do
    echo "  节点 $node 最近错误:"
    if ssh -o ConnectTimeout=3 $node "tail -20 /export/logs/kafka/kafka.log 2>/dev/null | grep -i error | tail -3" 2>/dev/null; then
        echo "    ✓ 日志检查完成"
    else
        echo "    ⚠ 无法获取日志信息"
    fi
done

# 清理临时文件
rm -f $TEMP_CONFIG

echo ""
echo "=== 集群检查完成 ==="
echo ""
echo "建议定期运行此脚本监控集群健康状态"
echo "如发现问题，请检查相应节点的日志文件"
