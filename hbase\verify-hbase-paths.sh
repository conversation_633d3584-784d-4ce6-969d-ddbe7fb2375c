#!/bin/bash

# HBase路径验证脚本
# 验证所有HBase相关路径配置是否正确

echo "=== HBase路径配置验证 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 预期路径配置
EXPECTED_HBASE_HOME="/export/server/hbase/hbase"
EXPECTED_HBASE_CONF="/export/server/hbase/hbase/conf"
EXPECTED_HBASE_BIN="/export/server/hbase/hbase/bin"
EXPECTED_HBASE_PIDS="/export/server/hbase/hbase/pids"

echo ""
echo "1. 检查HBase安装目录结构..."

# 检查主目录
if [ -d "/export/server/hbase" ]; then
    echo -e "  ${GREEN}✓${NC} /export/server/hbase 存在"
else
    echo -e "  ${RED}✗${NC} /export/server/hbase 不存在"
fi

# 检查HBase实际安装目录
if [ -d "$EXPECTED_HBASE_HOME" ]; then
    echo -e "  ${GREEN}✓${NC} $EXPECTED_HBASE_HOME 存在"
else
    echo -e "  ${RED}✗${NC} $EXPECTED_HBASE_HOME 不存在"
    echo -e "    ${YELLOW}请确认HBase是否正确安装到此目录${NC}"
fi

# 检查关键子目录
directories=("$EXPECTED_HBASE_CONF" "$EXPECTED_HBASE_BIN" "lib" "logs")
for dir in "${directories[@]}"; do
    full_path="$EXPECTED_HBASE_HOME/$dir"
    if [[ "$dir" == "$EXPECTED_HBASE_CONF" ]] || [[ "$dir" == "$EXPECTED_HBASE_BIN" ]]; then
        full_path="$dir"
    else
        full_path="$EXPECTED_HBASE_HOME/$dir"
    fi
    
    if [ -d "$full_path" ]; then
        echo -e "  ${GREEN}✓${NC} $full_path 存在"
    else
        echo -e "  ${YELLOW}⚠${NC} $full_path 不存在"
    fi
done

echo ""
echo "2. 检查配置文件中的路径..."

# 检查hbase-env.sh中的路径配置
if [ -f "hbase/conf/hbase-env.sh" ]; then
    echo "  检查 hbase-env.sh 配置..."
    
    # 检查HBASE_HOME配置
    hbase_home_config=$(grep "export HBASE_HOME=" hbase/conf/hbase-env.sh | grep -v "^#")
    if [[ $hbase_home_config == *"$EXPECTED_HBASE_HOME"* ]]; then
        echo -e "    ${GREEN}✓${NC} HBASE_HOME 配置正确: $EXPECTED_HBASE_HOME"
    else
        echo -e "    ${RED}✗${NC} HBASE_HOME 配置错误"
        echo "      当前配置: $hbase_home_config"
        echo "      期望配置: export HBASE_HOME=$EXPECTED_HBASE_HOME"
    fi
    
    # 检查HBASE_CONF_DIR配置
    hbase_conf_config=$(grep "export HBASE_CONF_DIR=" hbase/conf/hbase-env.sh | grep -v "^#")
    if [[ $hbase_conf_config == *"$EXPECTED_HBASE_CONF"* ]]; then
        echo -e "    ${GREEN}✓${NC} HBASE_CONF_DIR 配置正确: $EXPECTED_HBASE_CONF"
    else
        echo -e "    ${RED}✗${NC} HBASE_CONF_DIR 配置错误"
        echo "      当前配置: $hbase_conf_config"
        echo "      期望配置: export HBASE_CONF_DIR=$EXPECTED_HBASE_CONF"
    fi
    
    # 检查HBASE_PID_DIR配置
    hbase_pid_config=$(grep "export HBASE_PID_DIR=" hbase/conf/hbase-env.sh | grep -v "^#")
    if [[ $hbase_pid_config == *"$EXPECTED_HBASE_PIDS"* ]]; then
        echo -e "    ${GREEN}✓${NC} HBASE_PID_DIR 配置正确: $EXPECTED_HBASE_PIDS"
    else
        echo -e "    ${RED}✗${NC} HBASE_PID_DIR 配置错误"
        echo "      当前配置: $hbase_pid_config"
        echo "      期望配置: export HBASE_PID_DIR=$EXPECTED_HBASE_PIDS"
    fi
else
    echo -e "  ${RED}✗${NC} hbase/conf/hbase-env.sh 文件不存在"
fi

echo ""
echo "3. 检查启动脚本中的路径..."

# 检查start-hbase.sh
if [ -f "hbase/start-hbase.sh" ]; then
    echo "  检查 start-hbase.sh 脚本..."
    
    # 检查source路径
    source_line=$(grep "source.*hbase-env.sh" hbase/start-hbase.sh)
    if [[ $source_line == *"$EXPECTED_HBASE_CONF/hbase-env.sh"* ]]; then
        echo -e "    ${GREEN}✓${NC} hbase-env.sh source路径正确"
    else
        echo -e "    ${RED}✗${NC} hbase-env.sh source路径错误"
        echo "      当前: $source_line"
    fi
    
    # 检查bin目录路径
    bin_lines=$(grep "/export/server/hbase.*bin/" hbase/start-hbase.sh)
    if [[ $bin_lines == *"$EXPECTED_HBASE_HOME/bin"* ]]; then
        echo -e "    ${GREEN}✓${NC} HBase bin目录路径正确"
    else
        echo -e "    ${RED}✗${NC} HBase bin目录路径可能错误"
        echo "      找到的路径: $bin_lines"
    fi
else
    echo -e "  ${RED}✗${NC} hbase/start-hbase.sh 文件不存在"
fi

# 检查stop-hbase.sh
if [ -f "hbase/stop-hbase.sh" ]; then
    echo "  检查 stop-hbase.sh 脚本..."
    
    # 检查source路径
    source_line=$(grep "source.*hbase-env.sh" hbase/stop-hbase.sh)
    if [[ $source_line == *"$EXPECTED_HBASE_CONF/hbase-env.sh"* ]]; then
        echo -e "    ${GREEN}✓${NC} hbase-env.sh source路径正确"
    else
        echo -e "    ${RED}✗${NC} hbase-env.sh source路径错误"
        echo "      当前: $source_line"
    fi
    
    # 检查bin目录路径
    bin_lines=$(grep "/export/server/hbase.*bin/" hbase/stop-hbase.sh)
    if [[ $bin_lines == *"$EXPECTED_HBASE_HOME/bin"* ]]; then
        echo -e "    ${GREEN}✓${NC} HBase bin目录路径正确"
    else
        echo -e "    ${RED}✗${NC} HBase bin目录路径可能错误"
        echo "      找到的路径: $bin_lines"
    fi
else
    echo -e "  ${RED}✗${NC} hbase/stop-hbase.sh 文件不存在"
fi

echo ""
echo "4. 检查数据和日志目录..."

# 检查数据目录
data_dirs=("/export/data/hbase" "/export/logs/hbase")
for dir in "${data_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo -e "  ${GREEN}✓${NC} $dir 存在"
    else
        echo -e "  ${YELLOW}⚠${NC} $dir 不存在（启动时会自动创建）"
    fi
done

echo ""
echo "5. 生成修正建议..."

# 如果发现问题，提供修正建议
echo "如果发现路径配置错误，请按以下步骤修正："
echo ""
echo "1. 确认HBase实际安装路径："
echo "   ls -la /export/server/hbase/"
echo ""
echo "2. 如果HBase安装在 /export/server/hbase/hbase/，当前配置正确"
echo "3. 如果HBase安装在 /export/server/hbase/，需要修改配置文件"
echo ""
echo "4. 修正配置文件路径："
echo "   - 编辑 hbase/conf/hbase-env.sh"
echo "   - 编辑 hbase/start-hbase.sh"
echo "   - 编辑 hbase/stop-hbase.sh"
echo ""
echo "5. 创建必要目录："
echo "   mkdir -p /export/data/hbase"
echo "   mkdir -p /export/logs/hbase"
echo "   mkdir -p $EXPECTED_HBASE_PIDS"

echo ""
echo "=== 路径验证完成 ==="
