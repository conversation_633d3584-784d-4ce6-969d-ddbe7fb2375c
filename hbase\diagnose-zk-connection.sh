#!/bin/bash

# ZooKeeper连接诊断脚本

echo "=== ZooKeeper连接诊断 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# ZooKeeper服务器列表
ZK_SERVERS=("***************" "192.168.200.102" "192.168.200.103")
ZK_PORT="4181"

echo ""
echo "1. 检查ZooKeeper服务器连通性..."

zk_running=0
for server in "${ZK_SERVERS[@]}"; do
    echo "  检查 $server:$ZK_PORT..."
    
    # 检查端口是否开放
    if nc -z -w3 $server $ZK_PORT 2>/dev/null; then
        echo -e "    ${GREEN}✓${NC} 端口连通"
        
        # 检查ZooKeeper状态
        zk_status=$(echo ruok | nc -w3 $server $ZK_PORT 2>/dev/null)
        if [[ "$zk_status" == "imok" ]]; then
            echo -e "    ${GREEN}✓${NC} ZooKeeper运行正常"
            ((zk_running++))
            
            # 获取详细状态
            echo stat | nc -w3 $server $ZK_PORT 2>/dev/null | head -10 | sed 's/^/      /'
        else
            echo -e "    ${RED}✗${NC} ZooKeeper状态异常: $zk_status"
        fi
    else
        echo -e "    ${RED}✗${NC} 端口不通或服务未运行"
    fi
    echo ""
done

echo "ZooKeeper集群状态: $zk_running/3 节点运行中"

if [ $zk_running -eq 0 ]; then
    echo -e "${RED}错误: 没有可用的ZooKeeper节点${NC}"
    echo ""
    echo "解决方案:"
    echo "1. 启动ZooKeeper集群:"
    echo "   ./zookeeper/start-zookeeper.sh"
    echo ""
    echo "2. 检查ZooKeeper配置:"
    echo "   cat zookeeper/zoo.cfg"
    echo ""
    echo "3. 查看ZooKeeper日志:"
    echo "   tail -f /export/logs/zookeeper/zookeeper.log"
    exit 1
elif [ $zk_running -lt 2 ]; then
    echo -e "${YELLOW}警告: ZooKeeper集群节点不足，建议至少2个节点${NC}"
fi

echo ""
echo "2. 检查HBase配置中的ZooKeeper设置..."

# 检查hbase-site.xml中的ZooKeeper配置
if [ -f "hbase/conf/hbase-site.xml" ]; then
    echo "  检查 hbase-site.xml..."
    
    # 提取ZooKeeper配置
    zk_quorum=$(grep -A1 "hbase.zookeeper.quorum" hbase/conf/hbase-site.xml | grep "<value>" | sed 's/<[^>]*>//g' | tr -d ' ')
    zk_port=$(grep -A1 "hbase.zookeeper.property.clientPort" hbase/conf/hbase-site.xml | grep "<value>" | sed 's/<[^>]*>//g' | tr -d ' ')
    
    echo "    ZooKeeper Quorum: $zk_quorum"
    echo "    ZooKeeper Port: $zk_port"
    
    # 验证配置是否正确
    if [[ "$zk_quorum" == *"***************"* ]] && [[ "$zk_quorum" == *"192.168.200.102"* ]] && [[ "$zk_quorum" == *"192.168.200.103"* ]]; then
        echo -e "    ${GREEN}✓${NC} ZooKeeper Quorum配置正确"
    else
        echo -e "    ${RED}✗${NC} ZooKeeper Quorum配置错误"
    fi
    
    if [[ "$zk_port" == "4181" ]]; then
        echo -e "    ${GREEN}✓${NC} ZooKeeper端口配置正确"
    else
        echo -e "    ${RED}✗${NC} ZooKeeper端口配置错误，期望4181，实际$zk_port"
    fi
else
    echo -e "  ${RED}✗${NC} hbase-site.xml文件不存在"
fi

echo ""
echo "3. 检查网络连接..."

# 检查本机到ZooKeeper的网络连接
echo "  测试网络连接..."
for server in "${ZK_SERVERS[@]}"; do
    if ping -c 1 -W 3 $server >/dev/null 2>&1; then
        echo -e "    ${GREEN}✓${NC} $server 网络可达"
    else
        echo -e "    ${RED}✗${NC} $server 网络不可达"
    fi
done

echo ""
echo "4. 检查SASL认证配置..."

# 检查JAAS配置
if [ -f "hbase/conf/jaas.conf" ]; then
    echo -e "  ${GREEN}✓${NC} JAAS配置文件存在"
    
    # 检查Client配置
    if grep -q "Client {" hbase/conf/jaas.conf; then
        echo -e "    ${GREEN}✓${NC} Client配置块存在"
        
        # 提取用户名
        username=$(grep "username=" hbase/conf/jaas.conf | head -1 | cut -d'"' -f2)
        echo "      用户名: $username"
        
        # 检查密码是否存在
        if grep -q "password=" hbase/conf/jaas.conf; then
            echo -e "      ${GREEN}✓${NC} 密码配置存在"
        else
            echo -e "      ${RED}✗${NC} 密码配置缺失"
        fi
    else
        echo -e "    ${RED}✗${NC} Client配置块缺失"
    fi
else
    echo -e "  ${RED}✗${NC} JAAS配置文件不存在"
fi

echo ""
echo "5. 检查HBase运行模式配置..."

# 检查是否配置为分布式模式
if [ -f "hbase/conf/hbase-site.xml" ]; then
    distributed=$(grep -A1 "hbase.cluster.distributed" hbase/conf/hbase-site.xml | grep "<value>" | sed 's/<[^>]*>//g' | tr -d ' ')
    
    if [[ "$distributed" == "true" ]]; then
        echo -e "  ${GREEN}✓${NC} 配置为分布式模式"
    else
        echo -e "  ${YELLOW}⚠${NC} 配置为单机模式，但连接外部ZooKeeper集群"
        echo "    这可能导致连接问题"
    fi
fi

echo ""
echo "6. 生成解决方案..."

echo ""
echo "根据诊断结果，建议的解决方案:"
echo ""

if [ $zk_running -eq 0 ]; then
    echo "【优先级1】启动ZooKeeper集群:"
    echo "  1. 检查ZooKeeper配置: cat zookeeper/zoo.cfg"
    echo "  2. 启动ZooKeeper: ./zookeeper/start-zookeeper.sh"
    echo "  3. 验证启动: echo ruok | nc *************** 4181"
    echo ""
fi

echo "【优先级2】修正HBase配置:"
echo "  1. 确保hbase.cluster.distributed=true"
echo "  2. 验证ZooKeeper连接配置"
echo "  3. 检查SASL认证配置"
echo ""

echo "【优先级3】网络和防火墙:"
echo "  1. 确保4181端口开放"
echo "  2. 检查iptables/firewall规则"
echo "  3. 验证主机名解析"
echo ""

echo "【优先级4】重启服务:"
echo "  1. 重启ZooKeeper集群"
echo "  2. 等待ZooKeeper完全启动"
echo "  3. 重启HBase服务"

echo ""
echo "=== 诊断完成 ==="
