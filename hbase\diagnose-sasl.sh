#!/bin/bash

# HBase SASL认证诊断脚本

echo "=== HBase SASL认证诊断 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo "1. 检查ZooKeeper SASL认证状态..."

# 检查ZooKeeper连接
for zk_server in 192.168.200.101 192.168.200.102 192.168.200.103; do
    echo "  检查 $zk_server:4181..."
    if echo ruok | nc -w 3 $zk_server 4181 | grep -q imok; then
        echo -e "    ${GREEN}✓${NC} ZooKeeper $zk_server 运行正常"
        
        # 检查SASL状态
        echo "    检查SASL认证状态..."
        echo stat | nc -w 3 $zk_server 4181 | grep -i sasl
    else
        echo -e "    ${RED}✗${NC} ZooKeeper $zk_server 连接失败"
    fi
done

echo ""
echo "2. 检查HBase配置..."

# 检查hbase-site.xml中的SASL配置
echo "  检查 hbase-site.xml 配置..."
if [ -f "hbase/conf/hbase-site.xml" ]; then
    if grep -q "zookeeper.sasl.client" hbase/conf/hbase-site.xml; then
        echo -e "    ${GREEN}✓${NC} zookeeper.sasl.client 配置存在"
        sasl_value=$(grep -A1 "zookeeper.sasl.client" hbase/conf/hbase-site.xml | grep "<value>" | sed 's/<[^>]*>//g' | tr -d ' ')
        echo "      值: $sasl_value"
    else
        echo -e "    ${YELLOW}⚠${NC} zookeeper.sasl.client 配置缺失"
    fi
    
    if grep -q "zookeeper.sasl.clientconfig" hbase/conf/hbase-site.xml; then
        echo -e "    ${GREEN}✓${NC} zookeeper.sasl.clientconfig 配置存在"
        config_value=$(grep -A1 "zookeeper.sasl.clientconfig" hbase/conf/hbase-site.xml | grep "<value>" | sed 's/<[^>]*>//g' | tr -d ' ')
        echo "      值: $config_value"
    else
        echo -e "    ${YELLOW}⚠${NC} zookeeper.sasl.clientconfig 配置缺失"
    fi
else
    echo -e "    ${RED}✗${NC} hbase-site.xml 文件不存在"
fi

# 检查hbase-env.sh中的SASL配置
echo ""
echo "  检查 hbase-env.sh 配置..."
if [ -f "hbase/conf/hbase-env.sh" ]; then
    if grep -q "zookeeper.sasl.client=true" hbase/conf/hbase-env.sh; then
        echo -e "    ${GREEN}✓${NC} zookeeper.sasl.client=true 配置存在"
    else
        echo -e "    ${YELLOW}⚠${NC} zookeeper.sasl.client=true 配置缺失"
    fi
    
    if grep -q "java.security.auth.login.config" hbase/conf/hbase-env.sh; then
        echo -e "    ${GREEN}✓${NC} JAAS配置路径设置存在"
        jaas_path=$(grep "java.security.auth.login.config" hbase/conf/hbase-env.sh | head -1)
        echo "      配置: $jaas_path"
    else
        echo -e "    ${YELLOW}⚠${NC} JAAS配置路径设置缺失"
    fi
else
    echo -e "    ${RED}✗${NC} hbase-env.sh 文件不存在"
fi

echo ""
echo "3. 检查JAAS配置文件..."

if [ -f "hbase/conf/jaas.conf" ]; then
    echo -e "  ${GREEN}✓${NC} jaas.conf 文件存在"
    
    # 检查Client配置
    if grep -q "Client {" hbase/conf/jaas.conf; then
        echo -e "    ${GREEN}✓${NC} Client 配置块存在"
        
        # 检查用户名和密码
        if grep -q "username=" hbase/conf/jaas.conf; then
            username=$(grep "username=" hbase/conf/jaas.conf | head -1 | cut -d'"' -f2)
            echo "      用户名: $username"
        fi
        
        if grep -q "password=" hbase/conf/jaas.conf; then
            echo -e "      ${GREEN}✓${NC} 密码配置存在"
        fi
    else
        echo -e "    ${RED}✗${NC} Client 配置块缺失"
    fi
else
    echo -e "  ${RED}✗${NC} jaas.conf 文件不存在"
fi

echo ""
echo "4. 检查HBase进程状态..."

# 检查HBase进程
hbase_processes=$(ps aux | grep -E "(HMaster|HRegionServer)" | grep -v grep)
if [ -n "$hbase_processes" ]; then
    echo -e "  ${GREEN}✓${NC} HBase进程运行中:"
    echo "$hbase_processes" | sed 's/^/    /'
    
    # 检查进程的JVM参数
    echo ""
    echo "  检查HBase进程的SASL相关JVM参数..."
    for pid in $(ps aux | grep HMaster | grep -v grep | awk '{print $2}'); do
        echo "    HMaster (PID: $pid):"
        if ps -p $pid -o args --no-headers | grep -q "java.security.auth.login.config"; then
            echo -e "      ${GREEN}✓${NC} JAAS配置已加载"
        else
            echo -e "      ${RED}✗${NC} JAAS配置未加载"
        fi
        
        if ps -p $pid -o args --no-headers | grep -q "zookeeper.sasl.client=true"; then
            echo -e "      ${GREEN}✓${NC} ZooKeeper SASL客户端已启用"
        else
            echo -e "      ${RED}✗${NC} ZooKeeper SASL客户端未启用"
        fi
    done
else
    echo -e "  ${RED}✗${NC} 没有找到HBase进程"
fi

echo ""
echo "5. 检查HBase日志中的SASL信息..."

# 检查最新的HBase日志
log_dir="/export/logs/hbase"
if [ -d "$log_dir" ]; then
    latest_log=$(ls -t $log_dir/hbase-*.log 2>/dev/null | head -1)
    if [ -n "$latest_log" ]; then
        echo "  检查日志文件: $latest_log"
        
        # 检查SASL相关日志
        sasl_logs=$(tail -100 "$latest_log" | grep -i sasl)
        if [ -n "$sasl_logs" ]; then
            echo "    SASL相关日志:"
            echo "$sasl_logs" | tail -5 | sed 's/^/      /'
        else
            echo -e "    ${YELLOW}⚠${NC} 未找到SASL相关日志"
        fi
        
        # 检查认证相关日志
        auth_logs=$(tail -100 "$latest_log" | grep -i "auth\|login\|security")
        if [ -n "$auth_logs" ]; then
            echo "    认证相关日志:"
            echo "$auth_logs" | tail -3 | sed 's/^/      /'
        fi
        
        # 检查错误日志
        error_logs=$(tail -50 "$latest_log" | grep -i "error\|exception")
        if [ -n "$error_logs" ]; then
            echo -e "    ${RED}错误日志:${NC}"
            echo "$error_logs" | tail -3 | sed 's/^/      /'
        fi
    else
        echo -e "  ${YELLOW}⚠${NC} 未找到HBase日志文件"
    fi
else
    echo -e "  ${YELLOW}⚠${NC} HBase日志目录不存在: $log_dir"
fi

echo ""
echo "6. 修复建议..."

echo "如果SASL认证未正常工作，请尝试以下步骤："
echo ""
echo "1. 重启HBase服务:"
echo "   ./hbase/stop-hbase.sh"
echo "   sleep 5"
echo "   ./hbase/start-hbase.sh"
echo ""
echo "2. 检查配置文件是否正确复制到HBase安装目录:"
echo "   cp hbase/conf/* /export/server/hbase/hbase/conf/"
echo ""
echo "3. 验证ZooKeeper SASL认证:"
echo "   echo stat | nc 192.168.200.101 4181"
echo ""
echo "4. 查看详细的HBase启动日志:"
echo "   tail -f /export/logs/hbase/hbase-*.log"

echo ""
echo "=== 诊断完成 ==="
