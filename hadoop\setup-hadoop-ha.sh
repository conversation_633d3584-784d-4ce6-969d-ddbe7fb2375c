#!/bin/bash

# Hadoop HA集群部署脚本
# 适用于3节点HA集群部署

echo "=== Hadoop HA集群部署脚本 ==="

# 配置变量
HADOOP_HOME="/export/server/hadoop"
HADOOP_CONF_DIR="$HADOOP_HOME/etc/hadoop"
JAVA_HOME="/export/tools/jdk8"

# 节点配置
NAMENODE1="192.168.200.101"
NAMENODE2="192.168.200.102"
RESOURCEMANAGER1="192.168.200.101"
RESOURCEMANAGER2="192.168.200.102"
JOBHISTORY_SERVER="192.168.200.103"

NODES=("192.168.200.101" "192.168.200.102" "192.168.200.103")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查函数
check_prerequisites() {
    echo ""
    echo "1. 检查前置条件..."
    
    # 检查Java环境
    if [ ! -d "$JAVA_HOME" ]; then
        echo -e "  ${RED}✗${NC} Java环境不存在: $JAVA_HOME"
        exit 1
    fi
    echo -e "  ${GREEN}✓${NC} Java环境: $JAVA_HOME"
    
    # 检查Hadoop安装
    if [ ! -d "$HADOOP_HOME" ]; then
        echo -e "  ${RED}✗${NC} Hadoop未安装: $HADOOP_HOME"
        exit 1
    fi
    echo -e "  ${GREEN}✓${NC} Hadoop安装: $HADOOP_HOME"
    
    # 检查ZooKeeper
    for node in "${NODES[@]}"; do
        if ! nc -z $node 4181 2>/dev/null; then
            echo -e "  ${RED}✗${NC} ZooKeeper未运行: $node:4181"
            echo "  请先启动ZooKeeper集群"
            exit 1
        fi
    done
    echo -e "  ${GREEN}✓${NC} ZooKeeper集群运行正常"
}

# 创建目录
create_directories() {
    echo ""
    echo "2. 创建必要目录..."
    
    for node in "${NODES[@]}"; do
        echo "  在节点 $node 创建目录..."
        ssh root@$node "
            mkdir -p /export/data/hadoop/{namenode,datanode,journalnode,tmp}
            mkdir -p /export/data/hadoop/yarn/local
            mkdir -p /export/logs/hadoop/{yarn,mapred}
            mkdir -p /export/data/hadoop/mr-history/{tmp,done}
            mkdir -p $HADOOP_HOME/pids
            chown -R root:root /export/data/hadoop
            chown -R root:root /export/logs/hadoop
            chmod 755 /export/data/hadoop/*
            chmod 755 /export/logs/hadoop/*
        " 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "    ${GREEN}✓${NC} $node 目录创建完成"
        else
            echo -e "    ${YELLOW}⚠${NC} $node 目录创建失败（可能已存在）"
        fi
    done
}

# 分发配置文件
distribute_configs() {
    echo ""
    echo "3. 分发配置文件..."
    
    for node in "${NODES[@]}"; do
        echo "  分发配置到节点 $node..."
        scp -r hadoop/conf/* root@$node:$HADOOP_CONF_DIR/ 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "    ${GREEN}✓${NC} $node 配置分发完成"
        else
            echo -e "    ${RED}✗${NC} $node 配置分发失败"
        fi
    done
}

# 格式化NameNode
format_namenode() {
    echo ""
    echo "4. 格式化NameNode..."
    
    echo "  启动JournalNode集群..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/hdfs --daemon start journalnode" 2>/dev/null &
    done
    
    sleep 10
    echo -e "  ${GREEN}✓${NC} JournalNode集群启动完成"
    
    echo "  格式化NameNode1..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs namenode -format -force" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "  ${GREEN}✓${NC} NameNode1格式化完成"
    else
        echo -e "  ${RED}✗${NC} NameNode1格式化失败"
        exit 1
    fi
    
    echo "  启动NameNode1..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs --daemon start namenode" 2>/dev/null
    sleep 10
    
    echo "  同步NameNode2..."
    ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs namenode -bootstrapStandby -force" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "  ${GREEN}✓${NC} NameNode2同步完成"
    else
        echo -e "  ${RED}✗${NC} NameNode2同步失败"
        exit 1
    fi
    
    # 停止NameNode1
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs --daemon stop namenode" 2>/dev/null
}

# 初始化ZKFC
init_zkfc() {
    echo ""
    echo "5. 初始化ZKFC..."
    
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs zkfc -formatZK -force" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "  ${GREEN}✓${NC} ZKFC初始化完成"
    else
        echo -e "  ${RED}✗${NC} ZKFC初始化失败"
        exit 1
    fi
}

# 启动集群
start_cluster() {
    echo ""
    echo "6. 启动Hadoop集群..."
    
    # 启动HDFS
    echo "  启动HDFS集群..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/sbin/start-dfs.sh" 2>/dev/null
    sleep 15
    
    # 启动YARN
    echo "  启动YARN集群..."
    ssh root@$RESOURCEMANAGER1 "$HADOOP_HOME/sbin/start-yarn.sh" 2>/dev/null
    sleep 10
    
    # 启动ResourceManager2
    echo "  启动ResourceManager2..."
    ssh root@$RESOURCEMANAGER2 "$HADOOP_HOME/bin/yarn --daemon start resourcemanager" 2>/dev/null
    
    # 启动JobHistoryServer
    echo "  启动JobHistoryServer..."
    ssh root@$JOBHISTORY_SERVER "$HADOOP_HOME/bin/mapred --daemon start historyserver" 2>/dev/null
    
    echo -e "  ${GREEN}✓${NC} Hadoop集群启动完成"
}

# 检查集群状态
check_cluster_status() {
    echo ""
    echo "7. 检查集群状态..."
    
    # 检查NameNode状态
    echo "  检查NameNode状态..."
    for node in "$NAMENODE1" "$NAMENODE2"; do
        status=$(ssh root@$node "$HADOOP_HOME/bin/hdfs haadmin -getServiceState nn1 2>/dev/null" 2>/dev/null)
        if [[ $status == *"active"* ]] || [[ $status == *"standby"* ]]; then
            echo -e "    ${GREEN}✓${NC} $node NameNode: $status"
        else
            echo -e "    ${RED}✗${NC} $node NameNode状态异常"
        fi
    done
    
    # 检查ResourceManager状态
    echo "  检查ResourceManager状态..."
    for node in "$RESOURCEMANAGER1" "$RESOURCEMANAGER2"; do
        if ssh root@$node "netstat -tlnp | grep :8088" >/dev/null 2>&1; then
            echo -e "    ${GREEN}✓${NC} $node ResourceManager运行中"
        else
            echo -e "    ${RED}✗${NC} $node ResourceManager未运行"
        fi
    done
    
    # 检查DataNode状态
    echo "  检查DataNode状态..."
    datanode_count=$(ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs dfsadmin -report 2>/dev/null | grep 'Live datanodes' | awk '{print \$3}'" 2>/dev/null)
    echo -e "    ${GREEN}✓${NC} 活跃DataNode数量: $datanode_count"
    
    # 检查NodeManager状态
    echo "  检查NodeManager状态..."
    nodemanager_count=$(ssh root@$RESOURCEMANAGER1 "$HADOOP_HOME/bin/yarn node -list 2>/dev/null | grep RUNNING | wc -l" 2>/dev/null)
    echo -e "    ${GREEN}✓${NC} 活跃NodeManager数量: $nodemanager_count"
}

# 主函数
main() {
    echo "开始部署Hadoop HA集群..."
    echo "节点配置:"
    echo "  NameNode1: $NAMENODE1"
    echo "  NameNode2: $NAMENODE2"
    echo "  ResourceManager1: $RESOURCEMANAGER1"
    echo "  ResourceManager2: $RESOURCEMANAGER2"
    echo "  JobHistoryServer: $JOBHISTORY_SERVER"
    echo ""
    
    read -p "确认开始部署？(y/n): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "部署已取消"
        exit 0
    fi
    
    check_prerequisites
    create_directories
    distribute_configs
    format_namenode
    init_zkfc
    start_cluster
    check_cluster_status
    
    echo ""
    echo "=== Hadoop HA集群部署完成 ==="
    echo ""
    echo "Web界面访问地址:"
    echo "  NameNode1: http://$NAMENODE1:9870"
    echo "  NameNode2: http://$NAMENODE2:9870"
    echo "  ResourceManager1: http://$RESOURCEMANAGER1:8088"
    echo "  ResourceManager2: http://$RESOURCEMANAGER2:8088"
    echo "  JobHistoryServer: http://$JOBHISTORY_SERVER:19888"
    echo ""
    echo "常用命令:"
    echo "  检查NameNode状态: hdfs haadmin -getServiceState nn1"
    echo "  手动故障转移: hdfs haadmin -failover nn1 nn2"
    echo "  查看集群报告: hdfs dfsadmin -report"
    echo "  查看YARN节点: yarn node -list"
}

# 设置脚本权限
chmod +x hadoop/setup-hadoop-ha.sh

# 执行主函数
main
