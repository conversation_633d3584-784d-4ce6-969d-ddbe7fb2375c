// Kafka JAAS配置文件 - KRaft模式
// 适用于Kafka 4.0集群模式 - SASL/PLAIN认证

// Kafka Broker间通信认证
KafkaServer {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="admin"
    password="admin123"
    user_admin="admin123"
    user_producer="producer123"
    user_consumer="consumer123";
};

// Kafka客户端认证（管理员）
KafkaClient {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="admin"
    password="admin123";
};

// 生产者客户端认证
Producer {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="producer"
    password="producer123";
};

// 消费者客户端认证
Consumer {
    org.apache.kafka.common.security.plain.PlainLoginModule required
    username="consumer"
    password="consumer123";
};

// 注意：在KRaft模式下，此文件是可选的
// 实际认证配置在server.properties中的内联JAAS配置中定义
// 此文件主要用于客户端工具和外部应用程序
