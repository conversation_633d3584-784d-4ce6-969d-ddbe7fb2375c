#!/bin/bash

# Elasticsearch启动脚本

echo "=== 启动Elasticsearch ==="

# 设置Java环境
export JAVA_HOME=/export/tools/jdk17
export PATH=$JAVA_HOME/bin:$PATH

echo "Java环境: $JAVA_HOME"
echo "Java版本: $(java -version 2>&1 | head -n 1)"

# 检查Java版本
java_version_check=$(java -version 2>&1 | head -n 1)
if [[ $java_version_check == *"17."* ]] || [[ $java_version_check == *"11."* ]]; then
    echo "✓ Java版本兼容"
else
    echo "⚠ 警告: 建议使用Java 11或17"
fi

# 创建必要目录
echo ""
echo "创建必要目录..."
mkdir -p /export/data/elasticsearch/data
mkdir -p /export/data/elasticsearch/backup
mkdir -p /export/logs/elasticsearch

echo "✓ 目录创建完成"

# 检查配置文件
if [ ! -f "/export/server/elasticsearch/config/elasticsearch.yml" ]; then
    echo "✗ 错误: elasticsearch.yml配置文件不存在"
    echo "请确保配置文件已复制到 /export/server/elasticsearch/config/"
    exit 1
fi

echo "✓ 配置文件检查完成"

# 检查当前节点IP并确定配置文件
current_ip=$(hostname -I | awk '{print $1}')
echo "当前节点IP: $current_ip"

case $current_ip in
    "***************")
        node_name="es-node-1"
        ;;
    "***************")
        node_name="es-node-2"
        ;;
    "***************")
        node_name="es-node-3"
        ;;
    *)
        echo "⚠ 无法识别IP地址，使用默认配置"
        node_name="es-node-1"
        ;;
esac

echo "节点名称: $node_name"

# 设置系统参数
echo ""
echo "设置系统参数..."

# 设置内存锁定限制
ulimit -l unlimited

# 设置文件描述符限制
ulimit -n 65536

# 设置进程数限制
ulimit -u 4096

# 设置虚拟内存
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf > /dev/null
sudo sysctl -p > /dev/null 2>&1

echo "✓ 系统参数设置完成"

# 检查端口占用
echo ""
echo "检查端口占用..."
if netstat -tlnp | grep -q ":9200"; then
    echo "⚠ 端口9200已被占用"
    echo "正在检查是否为Elasticsearch进程..."
    if pgrep -f "org.elasticsearch.bootstrap.Elasticsearch" > /dev/null; then
        echo "✓ Elasticsearch已在运行"
        echo "PID: $(pgrep -f org.elasticsearch.bootstrap.Elasticsearch)"
        exit 0
    else
        echo "✗ 端口被其他进程占用"
        echo "请停止占用端口的进程或修改配置"
        exit 1
    fi
else
    echo "✓ 端口9200可用"
fi

if netstat -tlnp | grep -q ":9300"; then
    echo "⚠ 端口9300已被占用"
    if ! pgrep -f "org.elasticsearch.bootstrap.Elasticsearch" > /dev/null; then
        echo "✗ 传输端口被其他进程占用"
        exit 1
    fi
else
    echo "✓ 端口9300可用"
fi

# 启动Elasticsearch
echo ""
echo "启动Elasticsearch..."
cd /export/server/elasticsearch

# 后台启动
nohup bin/elasticsearch > /export/logs/elasticsearch/elasticsearch.log 2>&1 &
es_pid=$!

echo "Elasticsearch启动中，PID: $es_pid"
echo "日志文件: /export/logs/elasticsearch/elasticsearch.log"

# 等待启动
echo "等待Elasticsearch启动..."
sleep 30

# 检查进程
if ps -p $es_pid > /dev/null 2>&1; then
    echo "✓ Elasticsearch进程运行中 (PID: $es_pid)"
else
    echo "✗ Elasticsearch启动失败"
    echo "请检查日志: tail -50 /export/logs/elasticsearch/elasticsearch.log"
    exit 1
fi

# 检查端口
if netstat -tlnp 2>/dev/null | grep -q ":9200"; then
    echo "✓ HTTP端口9200正在监听"
else
    echo "⚠ HTTP端口9200未监听，可能还在启动中"
fi

if netstat -tlnp 2>/dev/null | grep -q ":9300"; then
    echo "✓ 传输端口9300正在监听"
else
    echo "⚠ 传输端口9300未监听，可能还在启动中"
fi

# 等待服务完全启动
echo ""
echo "等待服务完全启动..."
sleep 30

# 测试连接
echo ""
echo "测试连接..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:9200 | grep -q "200\|401"; then
    echo "✓ Elasticsearch HTTP服务可访问"
    
    # 显示集群信息
    echo ""
    echo "集群信息:"
    curl -s http://localhost:9200/_cluster/health?pretty 2>/dev/null || echo "需要认证才能访问集群信息"
else
    echo "⚠ Elasticsearch HTTP服务暂时不可访问，可能还在启动中"
fi

echo ""
echo "=== Elasticsearch启动完成 ==="
echo ""
echo "服务信息:"
echo "  HTTP端口: 9200"
echo "  传输端口: 9300"
echo "  节点名称: $node_name"
echo "  数据目录: /export/data/elasticsearch/data"
echo "  日志目录: /export/logs/elasticsearch"
echo ""
echo "监控命令:"
echo "  查看日志: tail -f /export/logs/elasticsearch/elasticsearch.log"
echo "  检查进程: ps aux | grep elasticsearch"
echo "  检查端口: netstat -tlnp | grep -E ':(9200|9300)'"
echo "  集群状态: curl http://localhost:9200/_cluster/health?pretty"
echo "  节点信息: curl http://localhost:9200/_nodes?pretty"
