#!/bin/bash

# HBase重启脚本 - 修复log4j配置

echo "=== 修复HBase log4j配置并重启 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo ""
echo "1. 停止当前HBase进程..."

# 停止HBase Master
if ps aux | grep -q "[H]Master"; then
    echo "  停止HBase Master..."
    /export/server/hbase/hbase/bin/hbase-daemon.sh stop master
    sleep 5
    
    # 强制终止如果还在运行
    if ps aux | grep -q "[H]Master"; then
        echo "  强制终止HBase Master..."
        pkill -f HMaster
        sleep 3
    fi
    echo -e "  ${GREEN}✓${NC} HBase Master已停止"
else
    echo -e "  ${YELLOW}⚠${NC} HBase Master未运行"
fi

echo ""
echo "2. 复制修正后的配置文件..."

# 复制配置文件到HBase安装目录
if [ -d "/export/server/hbase/hbase/conf" ]; then
    echo "  复制log4j.properties..."
    cp hbase/conf/log4j.properties /export/server/hbase/hbase/conf/
    
    echo "  复制其他配置文件..."
    cp hbase/conf/* /export/server/hbase/hbase/conf/
    
    echo -e "  ${GREEN}✓${NC} 配置文件复制完成"
else
    echo -e "  ${RED}✗${NC} HBase配置目录不存在"
    exit 1
fi

echo ""
echo "3. 验证log4j配置..."

# 检查log4j配置文件
if [ -f "/export/server/hbase/hbase/conf/log4j.properties" ]; then
    echo -e "  ${GREEN}✓${NC} log4j.properties文件存在"
    
    # 检查根日志器配置
    if grep -q "log4j.rootLogger" /export/server/hbase/hbase/conf/log4j.properties; then
        echo -e "  ${GREEN}✓${NC} 根日志器配置存在"
    else
        echo -e "  ${RED}✗${NC} 根日志器配置缺失"
    fi
else
    echo -e "  ${RED}✗${NC} log4j.properties文件不存在"
fi

echo ""
echo "4. 创建日志目录..."

# 确保日志目录存在
mkdir -p /export/logs/hbase
chown -R root:root /export/logs/hbase
chmod 755 /export/logs/hbase

echo -e "  ${GREEN}✓${NC} 日志目录准备完成"

echo ""
echo "5. 重新启动HBase..."

# 设置环境变量
export JAVA_HOME=/export/tools/jdk8
export HBASE_HOME=/export/server/hbase/hbase
export HBASE_CONF_DIR=/export/server/hbase/hbase/conf

# 启动HBase Master
echo "  启动HBase Master..."
/export/server/hbase/hbase/bin/hbase-daemon.sh start master

# 等待启动
echo "  等待HBase启动..."
sleep 15

echo ""
echo "6. 验证启动状态..."

# 检查进程
if ps aux | grep -q "[H]Master"; then
    echo -e "  ${GREEN}✓${NC} HBase Master进程运行中"
    
    # 获取进程ID
    master_pid=$(ps aux | grep "[H]Master" | awk '{print $2}')
    echo "    进程ID: $master_pid"
else
    echo -e "  ${RED}✗${NC} HBase Master进程未运行"
    echo ""
    echo "  检查启动日志:"
    echo "  tail -20 /export/logs/hbase/hbase-root-master-*.out"
    exit 1
fi

# 检查端口
echo "  检查端口监听..."
if netstat -tlnp 2>/dev/null | grep -q ":16010"; then
    echo -e "    ${GREEN}✓${NC} Web UI端口16010已监听"
else
    echo -e "    ${YELLOW}⚠${NC} Web UI端口16010未监听"
fi

if netstat -tlnp 2>/dev/null | grep -q ":16000"; then
    echo -e "    ${GREEN}✓${NC} Master端口16000已监听"
else
    echo -e "    ${YELLOW}⚠${NC} Master端口16000未监听"
fi

echo ""
echo "7. 检查日志输出..."

# 检查是否还有log4j警告
echo "  检查最新的启动日志..."
latest_out=$(ls -t /export/logs/hbase/hbase-root-master-*.out 2>/dev/null | head -1)
if [ -n "$latest_out" ]; then
    echo "    日志文件: $latest_out"
    
    # 检查log4j警告
    if tail -20 "$latest_out" | grep -q "log4j:WARN"; then
        echo -e "    ${YELLOW}⚠${NC} 仍有log4j警告"
        echo "    最新警告:"
        tail -20 "$latest_out" | grep "log4j:WARN" | tail -3 | sed 's/^/      /'
    else
        echo -e "    ${GREEN}✓${NC} 无log4j警告"
    fi
    
    # 显示最新几行日志
    echo "    最新日志:"
    tail -5 "$latest_out" | sed 's/^/      /'
fi

# 检查主日志文件
if [ -f "/export/logs/hbase/hbase.log" ]; then
    echo -e "  ${GREEN}✓${NC} 主日志文件已创建: /export/logs/hbase/hbase.log"
    echo "    最新日志条目:"
    tail -3 /export/logs/hbase/hbase.log | sed 's/^/      /'
else
    echo -e "  ${YELLOW}⚠${NC} 主日志文件尚未创建"
fi

echo ""
echo "8. 测试HBase连接..."

# 简单的连接测试
echo "  测试HBase Shell连接..."
timeout 10 /export/server/hbase/hbase/bin/hbase shell <<< "list" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "  ${GREEN}✓${NC} HBase Shell连接成功"
else
    echo -e "  ${YELLOW}⚠${NC} HBase Shell连接测试超时（可能仍在初始化）"
fi

echo ""
echo "=== HBase重启完成 ==="
echo ""
echo "服务信息:"
echo "  Web UI: http://192.168.200.101:16010"
echo "  Master: 192.168.200.101:16000"
echo "  Shell: /export/server/hbase/hbase/bin/hbase shell"
echo ""
echo "日志文件:"
echo "  启动日志: /export/logs/hbase/hbase-root-master-*.out"
echo "  主日志: /export/logs/hbase/hbase.log"
echo "  安全日志: /export/logs/hbase/hbase-security.log"
echo ""
echo "监控命令:"
echo "  查看进程: ps aux | grep HMaster"
echo "  查看端口: netstat -tlnp | grep -E '16000|16010'"
echo "  查看日志: tail -f /export/logs/hbase/hbase.log"
