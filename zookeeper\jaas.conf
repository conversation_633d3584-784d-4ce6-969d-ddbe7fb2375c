// JAAS配置文件 - ZooKeeper SASL认证
// 适用于ZooKeeper 3.8.4集群模式 - 安全增强版本

QuorumServer {
       org.apache.zookeeper.server.auth.DigestLoginModule required
       user_zkserver="Zk$erv3r@2024!"
       user_zkquorum="ZkQu0rum#2024!";
};

QuorumLearner {
       org.apache.zookeeper.server.auth.DigestLoginModule required
       username="zkserver"
       password="Zk$erv3r@2024!";
};

Server {
       org.apache.zookeeper.server.auth.DigestLoginModule required
       user_zkclient="ZkCl1ent#Secure"
       user_hbase="HBase$ecure2024"
       user_kafka="Kafka@Auth2024"
       user_admin="ZkAdm1n!2024";
};

Client {
       org.apache.zookeeper.server.auth.DigestLoginModule required
       username="zkclient"
       password="ZkCl1ent#Secure";
};
