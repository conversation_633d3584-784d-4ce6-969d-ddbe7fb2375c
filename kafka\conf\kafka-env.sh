#!/bin/bash

# Kafka Environment Configuration
# 适配ZooKeeper SASL认证

# Java环境变量 - 使用JDK 17（适配现代Kafka版本）
export JAVA_HOME=/export/tools/jdk17

# Kafka安装目录
export KAFKA_HOME=/export/server/kafka

# Kafka配置目录
export KAFKA_CONF_DIR=/export/server/kafka/config

# Kafka日志目录
export KAFKA_LOG_DIR=/export/logs/kafka

# Kafka数据目录
export KAFKA_DATA_DIR=/export/data/kafka

# Kafka 4.0 KRaft模式配置
# 不需要ZooKeeper相关配置

# Kafka JVM配置（JDK 17优化）
export KAFKA_HEAP_OPTS="-Xmx2G -Xms2G"
export KAFKA_JVM_PERFORMANCE_OPTS="-server -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35 -XX:+ExplicitGCInvokesConcurrent -Djava.awt.headless=true --add-opens=java.base/sun.nio.ch=ALL-UNNAMED"

# Kafka日志配置（Log4j2）
export KAFKA_LOG4J_OPTS="-Dlog4j2.configurationFile=file:$KAFKA_CONF_DIR/log4j2.yaml"

# Kafka GC日志配置（JDK 17兼容）
export KAFKA_GC_LOG_OPTS="-Xlog:gc*:$KAFKA_LOG_DIR/kafkaServer-gc.log:time,tags:filecount=10,filesize=100M"

# Kafka JMX配置（已禁用，避免端口冲突）
# export JMX_PORT=9999
# export KAFKA_JMX_OPTS="-Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.port=$JMX_PORT"

# 如需启用JMX，取消下面的注释并确保端口未被占用：
export JMX_PORT=9998
export KAFKA_JMX_OPTS="-Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.port=$JMX_PORT"

# 创建必要的目录
mkdir -p $KAFKA_LOG_DIR
mkdir -p $KAFKA_DATA_DIR
mkdir -p $KAFKA_DATA_DIR/logs

# 设置权限
chown -R appuser:appuser $KAFKA_LOG_DIR
chown -R appuser:appuser $KAFKA_DATA_DIR
chmod 755 $KAFKA_LOG_DIR
chmod 755 $KAFKA_DATA_DIR
