# Elasticsearch JVM配置文件
# 适配生产环境的JVM参数

################################################################
## IMPORTANT: JVM heap size
################################################################

# 设置JVM堆内存大小
# 建议设置为物理内存的50%，但不超过32GB
-Xms2g
-Xmx2g

################################################################
## Expert settings
################################################################

# 预分配整个堆内存
-XX:+AlwaysPreTouch

# 使用G1垃圾收集器（推荐用于大堆）
-XX:+UseG1GC

# G1垃圾收集器参数
-XX:G1HeapRegionSize=16m
-XX:+UseLargePages
-XX:+UnlockExperimentalVMOptions
-XX:+UseTransparentHugePages

# GC日志配置
-Xlog:gc*,gc+age=trace,safepoint:gc.log:time,level,tags
-XX:+UseGCLogFileRotation
-XX:NumberOfGCLogFiles=32
-XX:GCLogFileSize=64m

# 堆转储配置
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/export/logs/elasticsearch/

# JVM崩溃日志
-XX:ErrorFile=/export/logs/elasticsearch/hs_err_pid%p.log

# 确保UTF-8编码
-Dfile.encoding=UTF-8

# 使用服务器模式JVM
-server

# 减少停顿时间
-XX:+UseConcMarkSweepGC
-XX:CMSInitiatingOccupancyFraction=75
-XX:+UseCMSInitiatingOccupancyOnly

# 禁用偏向锁（在某些JVM版本中可能导致性能问题）
-XX:-UseBiasedLocking

# 启用压缩指针（64位JVM）
-XX:+UseCompressedOops

# 设置最大直接内存
-XX:MaxDirectMemorySize=1g

################################################################
## Security Manager
################################################################

# 启用安全管理器
-Djava.security.manager=default

################################################################
## Locale
################################################################

# 设置默认语言环境
-Duser.language=en
-Duser.country=US
-Duser.variant=

################################################################
## Network
################################################################

# 网络相关设置
-Djava.net.preferIPv4Stack=true

################################################################
## DNS
################################################################

# DNS缓存设置
-Dnetworkaddress.cache.ttl=60
-Dnetworkaddress.cache.negative.ttl=10

################################################################
## Temporary directory
################################################################

# 临时目录
-Djava.io.tmpdir=/tmp

################################################################
## JDK 9+ specific settings
################################################################

# 模块系统相关设置（JDK 9+）
--add-opens=java.base/java.io=ALL-UNNAMED
--add-opens=java.base/java.lang=ALL-UNNAMED
--add-opens=java.base/java.lang.invoke=ALL-UNNAMED
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED
--add-opens=java.base/java.net=ALL-UNNAMED
--add-opens=java.base/java.nio=ALL-UNNAMED
--add-opens=java.base/java.nio.channels=ALL-UNNAMED
--add-opens=java.base/java.nio.file=ALL-UNNAMED
--add-opens=java.base/java.security=ALL-UNNAMED
--add-opens=java.base/java.security.cert=ALL-UNNAMED
--add-opens=java.base/java.text=ALL-UNNAMED
--add-opens=java.base/java.time=ALL-UNNAMED
--add-opens=java.base/java.util=ALL-UNNAMED
--add-opens=java.base/java.util.concurrent=ALL-UNNAMED
--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
--add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED
--add-opens=java.base/java.util.regex=ALL-UNNAMED
--add-opens=java.base/javax.crypto=ALL-UNNAMED
--add-opens=java.base/javax.net.ssl=ALL-UNNAMED
--add-opens=java.base/javax.security.auth=ALL-UNNAMED
--add-opens=java.base/javax.security.auth.login=ALL-UNNAMED
--add-opens=java.base/sun.nio.ch=ALL-UNNAMED
--add-opens=java.base/sun.nio.cs=ALL-UNNAMED
--add-opens=java.base/sun.security.action=ALL-UNNAMED
--add-opens=java.base/sun.security.provider=ALL-UNNAMED
--add-opens=java.base/sun.security.ssl=ALL-UNNAMED
--add-opens=java.base/sun.security.util=ALL-UNNAMED
--add-opens=java.desktop/java.awt.font=ALL-UNNAMED
