#!/bin/bash

# Hadoop HA集群管理脚本
# 提供启动、停止、状态检查等功能

# 配置变量
HADOOP_HOME="/export/server/hadoop"
NAMENODE1="192.168.200.101"
NAMENODE2="192.168.200.102"
RESOURCEMANAGER1="192.168.200.101"
RESOURCEMANAGER2="192.168.200.102"
JOBHISTORY_SERVER="192.168.200.103"

NODES=("192.168.200.101" "192.168.200.102" "192.168.200.103")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示帮助信息
show_help() {
    echo "Hadoop HA集群管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start-all        启动整个集群"
    echo "  stop-all         停止整个集群"
    echo "  restart-all      重启整个集群"
    echo "  start-hdfs       启动HDFS集群"
    echo "  stop-hdfs        停止HDFS集群"
    echo "  start-yarn       启动YARN集群"
    echo "  stop-yarn        停止YARN集群"
    echo "  status           查看集群状态"
    echo "  failover         NameNode故障转移"
    echo "  check-health     健康检查"
    echo "  help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start-all     # 启动整个集群"
    echo "  $0 status        # 查看集群状态"
    echo "  $0 failover      # 执行NameNode故障转移"
}

# 启动HDFS集群
start_hdfs() {
    echo -e "${BLUE}启动HDFS集群...${NC}"
    
    # 启动JournalNode
    echo "  启动JournalNode..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/hdfs --daemon start journalnode" 2>/dev/null &
    done
    wait
    sleep 5
    
    # 启动NameNode
    echo "  启动NameNode..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs --daemon start namenode" 2>/dev/null &
    ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs --daemon start namenode" 2>/dev/null &
    wait
    sleep 10
    
    # 启动ZKFC
    echo "  启动ZKFC..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs --daemon start zkfc" 2>/dev/null &
    ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs --daemon start zkfc" 2>/dev/null &
    wait
    sleep 5
    
    # 启动DataNode
    echo "  启动DataNode..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/hdfs --daemon start datanode" 2>/dev/null &
    done
    wait
    
    echo -e "${GREEN}✓ HDFS集群启动完成${NC}"
}

# 停止HDFS集群
stop_hdfs() {
    echo -e "${BLUE}停止HDFS集群...${NC}"
    
    # 停止DataNode
    echo "  停止DataNode..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/hdfs --daemon stop datanode" 2>/dev/null &
    done
    wait
    
    # 停止ZKFC
    echo "  停止ZKFC..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs --daemon stop zkfc" 2>/dev/null &
    ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs --daemon stop zkfc" 2>/dev/null &
    wait
    
    # 停止NameNode
    echo "  停止NameNode..."
    ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs --daemon stop namenode" 2>/dev/null &
    ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs --daemon stop namenode" 2>/dev/null &
    wait
    
    # 停止JournalNode
    echo "  停止JournalNode..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/hdfs --daemon stop journalnode" 2>/dev/null &
    done
    wait
    
    echo -e "${GREEN}✓ HDFS集群停止完成${NC}"
}

# 启动YARN集群
start_yarn() {
    echo -e "${BLUE}启动YARN集群...${NC}"
    
    # 启动ResourceManager
    echo "  启动ResourceManager..."
    ssh root@$RESOURCEMANAGER1 "$HADOOP_HOME/bin/yarn --daemon start resourcemanager" 2>/dev/null &
    ssh root@$RESOURCEMANAGER2 "$HADOOP_HOME/bin/yarn --daemon start resourcemanager" 2>/dev/null &
    wait
    sleep 10
    
    # 启动NodeManager
    echo "  启动NodeManager..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/yarn --daemon start nodemanager" 2>/dev/null &
    done
    wait
    
    # 启动JobHistoryServer
    echo "  启动JobHistoryServer..."
    ssh root@$JOBHISTORY_SERVER "$HADOOP_HOME/bin/mapred --daemon start historyserver" 2>/dev/null
    
    echo -e "${GREEN}✓ YARN集群启动完成${NC}"
}

# 停止YARN集群
stop_yarn() {
    echo -e "${BLUE}停止YARN集群...${NC}"
    
    # 停止JobHistoryServer
    echo "  停止JobHistoryServer..."
    ssh root@$JOBHISTORY_SERVER "$HADOOP_HOME/bin/mapred --daemon stop historyserver" 2>/dev/null
    
    # 停止NodeManager
    echo "  停止NodeManager..."
    for node in "${NODES[@]}"; do
        ssh root@$node "$HADOOP_HOME/bin/yarn --daemon stop nodemanager" 2>/dev/null &
    done
    wait
    
    # 停止ResourceManager
    echo "  停止ResourceManager..."
    ssh root@$RESOURCEMANAGER1 "$HADOOP_HOME/bin/yarn --daemon stop resourcemanager" 2>/dev/null &
    ssh root@$RESOURCEMANAGER2 "$HADOOP_HOME/bin/yarn --daemon stop resourcemanager" 2>/dev/null &
    wait
    
    echo -e "${GREEN}✓ YARN集群停止完成${NC}"
}

# 查看集群状态
check_status() {
    echo -e "${BLUE}=== Hadoop集群状态 ===${NC}"
    
    # 检查NameNode状态
    echo ""
    echo -e "${YELLOW}NameNode状态:${NC}"
    nn1_status=$(ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs haadmin -getServiceState nn1 2>/dev/null" 2>/dev/null)
    nn2_status=$(ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs haadmin -getServiceState nn2 2>/dev/null" 2>/dev/null)
    
    if [[ $nn1_status == *"active"* ]]; then
        echo -e "  NameNode1 ($NAMENODE1): ${GREEN}Active${NC}"
    elif [[ $nn1_status == *"standby"* ]]; then
        echo -e "  NameNode1 ($NAMENODE1): ${YELLOW}Standby${NC}"
    else
        echo -e "  NameNode1 ($NAMENODE1): ${RED}Unknown/Stopped${NC}"
    fi
    
    if [[ $nn2_status == *"active"* ]]; then
        echo -e "  NameNode2 ($NAMENODE2): ${GREEN}Active${NC}"
    elif [[ $nn2_status == *"standby"* ]]; then
        echo -e "  NameNode2 ($NAMENODE2): ${YELLOW}Standby${NC}"
    else
        echo -e "  NameNode2 ($NAMENODE2): ${RED}Unknown/Stopped${NC}"
    fi
    
    # 检查DataNode状态
    echo ""
    echo -e "${YELLOW}DataNode状态:${NC}"
    datanode_count=$(ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs dfsadmin -report 2>/dev/null | grep 'Live datanodes' | awk '{print \$3}'" 2>/dev/null)
    echo -e "  活跃DataNode数量: ${GREEN}$datanode_count${NC}"
    
    # 检查ResourceManager状态
    echo ""
    echo -e "${YELLOW}ResourceManager状态:${NC}"
    for node in "$RESOURCEMANAGER1" "$RESOURCEMANAGER2"; do
        if ssh root@$node "netstat -tlnp | grep :8088" >/dev/null 2>&1; then
            echo -e "  ResourceManager ($node): ${GREEN}Running${NC}"
        else
            echo -e "  ResourceManager ($node): ${RED}Stopped${NC}"
        fi
    done
    
    # 检查NodeManager状态
    echo ""
    echo -e "${YELLOW}NodeManager状态:${NC}"
    nodemanager_count=$(ssh root@$RESOURCEMANAGER1 "$HADOOP_HOME/bin/yarn node -list 2>/dev/null | grep RUNNING | wc -l" 2>/dev/null)
    echo -e "  活跃NodeManager数量: ${GREEN}$nodemanager_count${NC}"
    
    # 检查JobHistoryServer状态
    echo ""
    echo -e "${YELLOW}JobHistoryServer状态:${NC}"
    if ssh root@$JOBHISTORY_SERVER "netstat -tlnp | grep :19888" >/dev/null 2>&1; then
        echo -e "  JobHistoryServer ($JOBHISTORY_SERVER): ${GREEN}Running${NC}"
    else
        echo -e "  JobHistoryServer ($JOBHISTORY_SERVER): ${RED}Stopped${NC}"
    fi
    
    # Web界面地址
    echo ""
    echo -e "${YELLOW}Web界面地址:${NC}"
    echo "  NameNode1: http://$NAMENODE1:9870"
    echo "  NameNode2: http://$NAMENODE2:9870"
    echo "  ResourceManager1: http://$RESOURCEMANAGER1:8088"
    echo "  ResourceManager2: http://$RESOURCEMANAGER2:8088"
    echo "  JobHistoryServer: http://$JOBHISTORY_SERVER:19888"
}

# NameNode故障转移
namenode_failover() {
    echo -e "${BLUE}执行NameNode故障转移...${NC}"
    
    # 获取当前状态
    nn1_status=$(ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs haadmin -getServiceState nn1 2>/dev/null" 2>/dev/null)
    nn2_status=$(ssh root@$NAMENODE2 "$HADOOP_HOME/bin/hdfs haadmin -getServiceState nn2 2>/dev/null" 2>/dev/null)
    
    echo "当前状态:"
    echo "  NameNode1: $nn1_status"
    echo "  NameNode2: $nn2_status"
    
    if [[ $nn1_status == *"active"* ]]; then
        echo "执行故障转移: nn1 -> nn2"
        ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs haadmin -failover nn1 nn2" 2>/dev/null
    elif [[ $nn2_status == *"active"* ]]; then
        echo "执行故障转移: nn2 -> nn1"
        ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs haadmin -failover nn2 nn1" 2>/dev/null
    else
        echo -e "${RED}错误: 没有找到Active NameNode${NC}"
        exit 1
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 故障转移完成${NC}"
        sleep 5
        check_status
    else
        echo -e "${RED}✗ 故障转移失败${NC}"
        exit 1
    fi
}

# 健康检查
health_check() {
    echo -e "${BLUE}=== 集群健康检查 ===${NC}"
    
    # 检查磁盘空间
    echo ""
    echo -e "${YELLOW}磁盘空间检查:${NC}"
    for node in "${NODES[@]}"; do
        disk_usage=$(ssh root@$node "df -h /export/data | tail -1 | awk '{print \$5}' | sed 's/%//'" 2>/dev/null)
        if [ "$disk_usage" -lt 80 ]; then
            echo -e "  $node: ${GREEN}$disk_usage%${NC}"
        elif [ "$disk_usage" -lt 90 ]; then
            echo -e "  $node: ${YELLOW}$disk_usage%${NC}"
        else
            echo -e "  $node: ${RED}$disk_usage%${NC}"
        fi
    done
    
    # 检查内存使用
    echo ""
    echo -e "${YELLOW}内存使用检查:${NC}"
    for node in "${NODES[@]}"; do
        mem_usage=$(ssh root@$node "free | grep Mem | awk '{printf \"%.1f\", \$3/\$2 * 100.0}'" 2>/dev/null)
        echo -e "  $node: ${GREEN}$mem_usage%${NC}"
    done
    
    # 检查HDFS健康状态
    echo ""
    echo -e "${YELLOW}HDFS健康状态:${NC}"
    hdfs_report=$(ssh root@$NAMENODE1 "$HADOOP_HOME/bin/hdfs dfsadmin -report 2>/dev/null" 2>/dev/null)
    if [[ $hdfs_report == *"Live datanodes"* ]]; then
        echo -e "  HDFS状态: ${GREEN}正常${NC}"
    else
        echo -e "  HDFS状态: ${RED}异常${NC}"
    fi
}

# 主函数
main() {
    case "$1" in
        "start-all")
            start_hdfs
            sleep 10
            start_yarn
            ;;
        "stop-all")
            stop_yarn
            sleep 5
            stop_hdfs
            ;;
        "restart-all")
            stop_yarn
            sleep 5
            stop_hdfs
            sleep 10
            start_hdfs
            sleep 10
            start_yarn
            ;;
        "start-hdfs")
            start_hdfs
            ;;
        "stop-hdfs")
            stop_hdfs
            ;;
        "start-yarn")
            start_yarn
            ;;
        "stop-yarn")
            stop_yarn
            ;;
        "status")
            check_status
            ;;
        "failover")
            namenode_failover
            ;;
        "check-health")
            health_check
            ;;
        "help"|"")
            show_help
            ;;
        *)
            echo "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
