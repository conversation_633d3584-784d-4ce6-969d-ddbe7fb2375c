# Kafka 4.0 KRaft模式配置 - SASL/PLAIN认证

## 概述

本配置适用于Kafka 4.0 KRaft模式，使用SASL/PLAIN密码认证，不依赖ZooKeeper。

## 服务信息

- **Kafka服务**: ***************:9092 (SASL_PLAINTEXT)
- **Controller端口**: 9093
- **数据存储**: /export/data/kafka/logs
- **元数据存储**: /export/data/kafka/metadata
- **日志目录**: /export/logs/kafka
- **Java版本**: JDK 17

## 认证信息

### SASL/PLAIN用户账号：
- **管理员**: admin / admin123
- **生产者**: producer / producer123
- **消费者**: consumer / consumer123

## 配置文件说明

### 1. server.properties
- Kafka 4.0 KRaft模式主配置文件
- 包含SASL/PLAIN认证配置
- 单节点broker+controller模式

### 2. kafka-env.sh
- Kafka环境变量配置
- JDK 17兼容的JVM参数
- 目录和日志配置

### 3. log4j2.yaml
- Log4j2日志配置（YAML格式）
- 支持日志轮转和大小限制

### 4. consumer.properties / producer.properties
- 客户端配置文件，包含SASL认证
- 生产者和消费者的认证配置

## 部署步骤

### 1. 前置条件检查
```bash
# 检查Java 17安装
java -version
# 应该显示: openjdk version "17.x.x"

# 检查Kafka安装
ls -la /export/server/kafka/bin/kafka-server-start.sh
```

### 2. 复制配置文件
```bash
# 将配置文件复制到Kafka安装目录
cp kafka/conf/* /export/server/kafka/config/

# 设置执行权限
chmod +x kafka/start-kafka4.sh
chmod +x kafka/stop-kafka4.sh
```

### 3. 配置节点信息
**单节点模式（默认）**：
- 配置已设置为单节点broker+controller模式
- node.id=1，监听localhost:9093（controller）和0.0.0.0:9092（broker）

**多节点集群模式**：
如需配置多节点，修改以下参数：

**节点1 (***************)**:
```bash
# 修改 server.properties
node.id=1
controller.quorum.voters=1@***************:9093,2@***************:9093,3@***************:9093
listeners=SASL_PLAINTEXT://***************:9092,CONTROLLER://***************:9093
advertised.listeners=SASL_PLAINTEXT://***************:9092
```

**节点2 (***************)**:
```bash
node.id=2
controller.quorum.voters=1@***************:9093,2@***************:9093,3@***************:9093
listeners=SASL_PLAINTEXT://***************:9092,CONTROLLER://***************:9093
advertised.listeners=SASL_PLAINTEXT://***************:9092
```

**节点3 (***************)**:
```bash
node.id=3
controller.quorum.voters=1@***************:9093,2@***************:9093,3@***************:9093
listeners=SASL_PLAINTEXT://***************:9092,CONTROLLER://***************:9093
advertised.listeners=SASL_PLAINTEXT://***************:9092
```

### 4. 启动Kafka
```bash
# 使用Kafka 4.0启动脚本
chmod +x kafka/start-kafka4.sh
./kafka/start-kafka4.sh

# 或手动启动
export JAVA_HOME=/export/tools/jdk17
export PATH=$JAVA_HOME/bin:$PATH
cd /export/server/kafka

# 首次启动需要格式化存储
CLUSTER_ID=$(bin/kafka-storage.sh random-uuid)
bin/kafka-storage.sh format --config config/server.properties --cluster-id $CLUSTER_ID

# 启动Kafka
bin/kafka-server-start.sh config/server.properties
```

### 5. 验证启动状态
```bash
# 检查进程
ps aux | grep kafka.Kafka

# 检查端口
netstat -tlnp | grep -E ":(9092|9093)"

# 检查日志
tail -f /export/logs/kafka/kafka.log
```

## 测试SASL/PLAIN认证

### 1. 创建客户端配置文件
```bash
cat > client.properties << EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";
EOF
```

### 2. 验证连接和认证
```bash
cd /export/server/kafka

# 测试连接（使用实际的broker地址）
bin/kafka-topics.sh --list --bootstrap-server ***************:9092 --command-config client.properties

# 或者连接到集群（推荐）
bin/kafka-topics.sh --list --bootstrap-server ***************:9092,***************:9092,***************:9092 --command-config client.properties

# 应该返回空列表或现有topic列表，而不是认证错误
```

## Kafka 4.0基本操作

### 1. 创建Topic（需要认证）
```bash
cd /export/server/kafka

# 创建客户端配置（如果还没有）
cat > client.properties << EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin123";
EOF

# 创建topic
bin/kafka-topics.sh \
  --create \
  --topic test-topic \
  --bootstrap-server ***************:9092 \
  --partitions 3 \
  --replication-factor 1 \
  --command-config client.properties
```

### 2. 查看Topic列表
```bash
bin/kafka-topics.sh \
  --list \
  --bootstrap-server ***************:9092 \
  --command-config client.properties
```

### 3. 查看Topic详情
```bash
bin/kafka-topics.sh \
  --describe \
  --topic test-topic \
  --bootstrap-server localhost:9092 \
  --command-config client.properties
```

### 4. 生产消息（使用producer用户）
```bash
# 创建生产者配置
cat > producer-client.properties << EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="producer" password="producer123";
EOF

# 发送消息
bin/kafka-console-producer.sh \
  --topic test-topic \
  --bootstrap-server localhost:9092 \
  --producer.config producer-client.properties
```

### 5. 消费消息（使用consumer用户）
```bash
# 创建消费者配置
cat > consumer-client.properties << EOF
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="consumer" password="consumer123";
EOF

# 消费消息
bin/kafka-console-consumer.sh \
  --topic test-topic \
  --bootstrap-server localhost:9092 \
  --consumer.config consumer-client.properties \
  --from-beginning
```

## 监控和维护

### 1. 日志位置
- 主日志：`/export/logs/kafka/kafka.log`
- GC日志：`/export/logs/kafka/kafkaServer-gc.log`
- 元数据存储：`/export/data/kafka/metadata/`
- 数据存储：`/export/data/kafka/logs/`

### 2. 性能调优
- 根据实际负载调整JVM内存（默认2GB）
- 调整批处理大小和延迟参数
- 监控磁盘I/O和网络使用情况
- KRaft模式比ZooKeeper模式性能更好

### 3. 集群管理
```bash
# 查看集群元数据
bin/kafka-metadata-shell.sh --snapshot /export/data/kafka/metadata/__cluster_metadata-0/00000000000000000000.log

# 查看broker信息
bin/kafka-broker-api-versions.sh --bootstrap-server localhost:9092 --command-config client.properties
```

## 故障排查

### 1. 常见问题
- **Java版本错误**: 确保使用JDK 17
- **认证失败**: 检查SASL/PLAIN用户名密码
- **端口占用**: 检查9092和9093端口是否被占用
- **存储格式化失败**: 检查配置文件语法错误

### 2. 调试命令
```bash
# 检查Kafka进程
ps aux | grep kafka.Kafka

# 检查端口监听
netstat -tlnp | grep -E ":(9092|9093)"

# 查看启动日志
tail -f /export/logs/kafka/kafka.log

# 检查集群元数据
bin/kafka-metadata-shell.sh --snapshot /export/data/kafka/metadata/__cluster_metadata-0/00000000000000000000.log

# 检查消费者组
bin/kafka-consumer-groups.sh --bootstrap-server localhost:9092 --command-config client.properties --list
```

### 3. 重置和清理
```bash
# 停止Kafka
./kafka/stop-kafka4.sh

# 清理元数据（重新开始）
rm -rf /export/data/kafka/metadata/*
rm -rf /export/data/kafka/logs/*

# 重新格式化和启动
./kafka/start-kafka4.sh
```

## 停止服务
```bash
# 使用停止脚本
./kafka/stop-kafka4.sh

# 或手动停止
/export/server/kafka/bin/kafka-server-stop.sh
```

## 重要说明

### Kafka 4.0 KRaft模式特点：
1. **不依赖ZooKeeper**: 使用内置的KRaft协议管理元数据
2. **更好的性能**: 启动更快，延迟更低
3. **简化运维**: 减少组件依赖，更容易管理
4. **SASL/PLAIN认证**: 简单的用户名密码认证
5. **单节点模式**: 支持开发和测试环境的单节点部署

### 注意事项：
1. **Java版本**: 必须使用JDK 17
2. **存储格式化**: 首次启动需要格式化KRaft存储
3. **认证配置**: 所有客户端连接都需要SASL认证
4. **网络配置**: 确保防火墙允许9092和9093端口
5. **资源配置**: 根据实际环境调整JVM内存（默认2GB）
