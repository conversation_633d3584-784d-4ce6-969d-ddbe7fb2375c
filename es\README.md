# Elasticsearch 8.x 集群配置

## 概述

本配置适用于Elasticsearch 8.x三节点集群部署，包含安全认证和SSL传输加密。

## 集群信息

- **集群名称**: es-cluster-001
- **节点列表**: 
  - 节点1: 192.168.200.101:9200 (es-node-1)
  - 节点2: ***************:9200 (es-node-2)  
  - 节点3: ***************:9200 (es-node-3)
- **传输端口**: 9300
- **数据目录**: /export/data/elasticsearch/data
- **日志目录**: /export/logs/elasticsearch
- **Java版本**: JDK 11/17

## 配置文件说明

### 1. elasticsearch.yml
- 主配置文件（节点1）
- 包含集群、网络、安全等配置
- 启用X-Pack安全功能

### 2. elasticsearch-node2.yml / elasticsearch-node3.yml
- 节点2和节点3的专用配置
- 主要差异：node.name和network.host

### 3. jvm.options
- JVM参数配置
- 堆内存设置（默认2GB）
- G1垃圾收集器配置
- JDK 9+模块系统兼容

### 4. log4j2.properties
- 日志配置文件
- 支持日志轮转和分类
- 包含慢查询日志配置

## 部署步骤

### 1. 前置条件检查
```bash
# 检查Java版本
java -version
# 应该显示: openjdk version "17.x.x" 或 "11.x.x"

# 检查系统资源
free -h
df -h /export
```

### 2. 系统参数配置
```bash
# 设置虚拟内存
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 设置文件描述符限制
echo '* soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* soft nproc 4096' | sudo tee -a /etc/security/limits.conf
echo '* hard nproc 4096' | sudo tee -a /etc/security/limits.conf

# 禁用交换分区
sudo swapoff -a
```

### 3. 复制配置文件
```bash
# 复制配置文件到Elasticsearch安装目录
cp es/conf/elasticsearch.yml /export/server/elasticsearch/config/
cp es/conf/jvm.options /export/server/elasticsearch/config/
cp es/conf/log4j2.properties /export/server/elasticsearch/config/

# 设置执行权限
chmod +x es/start-elasticsearch.sh
chmod +x es/stop-elasticsearch.sh
chmod +x es/setup-security.sh
```

### 4. 配置各节点
**节点1 (192.168.200.101)**:
```bash
# 使用默认配置文件
cp es/conf/elasticsearch.yml /export/server/elasticsearch/config/
```

**节点2 (***************)**:
```bash
# 使用节点2配置
cp es/conf/elasticsearch-node2.yml /export/server/elasticsearch/config/elasticsearch.yml
```

**节点3 (***************)**:
```bash
# 使用节点3配置
cp es/conf/elasticsearch-node3.yml /export/server/elasticsearch/config/elasticsearch.yml
```

### 5. 安全配置
```bash
# 在节点1上生成证书
./es/setup-security.sh

# 证书会自动复制到其他节点（需要SSH免密）
# 或手动复制证书到其他节点
```

### 6. 启动集群
```bash
# 在每个节点上启动Elasticsearch
./es/start-elasticsearch.sh

# 检查启动状态
tail -f /export/logs/elasticsearch/elasticsearch.log
```

## 安全配置

### 1. 生成证书
```bash
# 运行安全配置脚本
./es/setup-security.sh
```

### 2. 设置用户密码
```bash
# 启动Elasticsearch后设置密码
cd /export/server/elasticsearch

# 自动生成密码
bin/elasticsearch-setup-passwords auto

# 或交互式设置密码
bin/elasticsearch-setup-passwords interactive
```

### 3. 内置用户
- **elastic**: 超级用户
- **kibana_system**: Kibana系统用户
- **logstash_system**: Logstash系统用户
- **beats_system**: Beats系统用户
- **apm_system**: APM系统用户
- **remote_monitoring_user**: 远程监控用户

## 基本操作

### 1. 集群状态检查
```bash
# 集群健康状态
curl -u elastic:password http://192.168.200.101:9200/_cluster/health?pretty

# 节点信息
curl -u elastic:password http://192.168.200.101:9200/_nodes?pretty

# 集群设置
curl -u elastic:password http://192.168.200.101:9200/_cluster/settings?pretty
```

### 2. 索引操作
```bash
# 创建索引
curl -X PUT -u elastic:password "http://192.168.200.101:9200/test-index" -H "Content-Type: application/json" -d '{
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1
  }
}'

# 查看索引
curl -u elastic:password http://192.168.200.101:9200/_cat/indices?v

# 删除索引
curl -X DELETE -u elastic:password http://192.168.200.101:9200/test-index
```

### 3. 文档操作
```bash
# 添加文档
curl -X POST -u elastic:password "http://192.168.200.101:9200/test-index/_doc" -H "Content-Type: application/json" -d '{
  "title": "Test Document",
  "content": "This is a test document",
  "timestamp": "2024-01-01T00:00:00Z"
}'

# 搜索文档
curl -u elastic:password "http://192.168.200.101:9200/test-index/_search?pretty"

# 按条件搜索
curl -X GET -u elastic:password "http://192.168.200.101:9200/test-index/_search" -H "Content-Type: application/json" -d '{
  "query": {
    "match": {
      "title": "test"
    }
  }
}'
```

## 监控和维护

### 1. 日志位置
- 主日志: `/export/logs/elasticsearch/es-cluster-001.log`
- 慢查询日志: `/export/logs/elasticsearch/es-cluster-001_index_search_slowlog.log`
- 慢索引日志: `/export/logs/elasticsearch/es-cluster-001_index_indexing_slowlog.log`
- GC日志: `/export/logs/elasticsearch/gc.log`

### 2. 性能监控
```bash
# 集群统计
curl -u elastic:password http://192.168.200.101:9200/_cluster/stats?pretty

# 节点统计
curl -u elastic:password http://192.168.200.101:9200/_nodes/stats?pretty

# 索引统计
curl -u elastic:password http://192.168.200.101:9200/_stats?pretty
```

### 3. 性能调优
- 根据实际负载调整JVM堆内存
- 调整分片和副本数量
- 监控磁盘I/O和网络使用情况
- 定期清理旧索引和日志

## 故障排查

### 1. 常见问题
- **集群状态为红色**: 检查分片分配和节点状态
- **内存不足**: 调整JVM堆内存设置
- **磁盘空间不足**: 清理旧数据或增加存储
- **网络连接问题**: 检查防火墙和网络配置

### 2. 调试命令
```bash
# 检查集群分配
curl -u elastic:password http://192.168.200.101:9200/_cluster/allocation/explain?pretty

# 检查分片状态
curl -u elastic:password http://192.168.200.101:9200/_cat/shards?v

# 检查节点状态
curl -u elastic:password http://192.168.200.101:9200/_cat/nodes?v

# 检查待处理任务
curl -u elastic:password http://192.168.200.101:9200/_cluster/pending_tasks?pretty
```

### 3. 重启和恢复
```bash
# 停止服务
./es/stop-elasticsearch.sh

# 清理锁文件（如果需要）
rm -f /export/data/elasticsearch/data/nodes/*/node.lock

# 重新启动
./es/start-elasticsearch.sh
```

## 停止服务
```bash
# 使用停止脚本
./es/stop-elasticsearch.sh

# 或手动停止
pkill -f org.elasticsearch.bootstrap.Elasticsearch
```

## 注意事项

1. **内存设置**: JVM堆内存不应超过物理内存的50%，且不超过32GB
2. **磁盘空间**: 确保数据目录有足够空间，建议保留15%以上空闲空间
3. **网络配置**: 确保所有节点间网络互通，防火墙允许9200和9300端口
4. **时间同步**: 所有节点时间必须同步
5. **安全配置**: 生产环境必须启用安全功能并设置强密码
6. **备份策略**: 定期备份重要数据和配置文件
7. **版本兼容**: 确保所有节点使用相同版本的Elasticsearch
