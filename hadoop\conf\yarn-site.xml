<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- <PERSON><PERSON> YARN HA集群配置 -->
    
    <!-- ResourceManager HA配置 -->
    <property>
        <name>yarn.resourcemanager.ha.enabled</name>
        <value>true</value>
        <description>启用ResourceManager HA</description>
    </property>
    
    <property>
        <name>yarn.resourcemanager.cluster-id</name>
        <value>yarn-cluster</value>
        <description>YARN集群ID</description>
    </property>
    
    <property>
        <name>yarn.resourcemanager.ha.rm-ids</name>
        <value>rm1,rm2</value>
        <description>ResourceManager节点标识</description>
    </property>
    
    <!-- ResourceManager地址配置 -->
    <property>
        <name>yarn.resourcemanager.hostname.rm1</name>
        <value>***************</value>
        <description>ResourceManager1主机名</description>
    </property>
    
    <property>
        <name>yarn.resourcemanager.hostname.rm2</name>
        <value>***************</value>
        <description>ResourceManager2主机名</description>
    </property>
    
    <!-- ResourceManager Web地址 -->
    <property>
        <name>yarn.resourcemanager.webapp.address.rm1</name>
        <value>***************:8088</value>
        <description>ResourceManager1 Web地址</description>
    </property>
    
    <property>
        <name>yarn.resourcemanager.webapp.address.rm2</name>
        <value>***************:8088</value>
        <description>ResourceManager2 Web地址</description>
    </property>
    
    <!-- ZooKeeper配置 -->
    <property>
        <name>yarn.resourcemanager.zk-address</name>
        <value>***************:4181,***************:4181,***************:4181</value>
        <description>ZooKeeper集群地址</description>
    </property>
    
    <!-- NodeManager配置 -->
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
        <description>NodeManager辅助服务</description>
    </property>
    
    <property>
        <name>yarn.nodemanager.aux-services.mapreduce_shuffle.class</name>
        <value>org.apache.hadoop.mapred.ShuffleHandler</value>
        <description>Shuffle服务类</description>
    </property>
    
    <!-- 资源配置 -->
    <property>
        <name>yarn.nodemanager.resource.memory-mb</name>
        <value>4096</value>
        <description>NodeManager可用内存(MB)</description>
    </property>
    
    <property>
        <name>yarn.nodemanager.resource.cpu-vcores</name>
        <value>4</value>
        <description>NodeManager可用CPU核心数</description>
    </property>
    
    <property>
        <name>yarn.scheduler.maximum-allocation-mb</name>
        <value>4096</value>
        <description>单个容器最大内存</description>
    </property>
    
    <property>
        <name>yarn.scheduler.minimum-allocation-mb</name>
        <value>512</value>
        <description>单个容器最小内存</description>
    </property>
    
    <!-- ApplicationMaster配置 -->
    <property>
        <name>yarn.app.mapreduce.am.resource.mb</name>
        <value>1024</value>
        <description>ApplicationMaster内存</description>
    </property>
    
    <!-- 日志聚合 -->
    <property>
        <name>yarn.log-aggregation-enable</name>
        <value>true</value>
        <description>启用日志聚合</description>
    </property>
    
    <property>
        <name>yarn.log-aggregation.retain-seconds</name>
        <value>604800</value>
        <description>日志保留时间(秒)</description>
    </property>
    
    <!-- NodeManager本地目录 -->
    <property>
        <name>yarn.nodemanager.local-dirs</name>
        <value>/export/data/hadoop/yarn/local</value>
        <description>NodeManager本地目录</description>
    </property>
    
    <property>
        <name>yarn.nodemanager.log-dirs</name>
        <value>/export/logs/hadoop/yarn</value>
        <description>NodeManager日志目录</description>
    </property>
    
    <!-- 容器执行器 -->
    <property>
        <name>yarn.nodemanager.container-executor.class</name>
        <value>org.apache.hadoop.yarn.server.nodemanager.DefaultContainerExecutor</value>
        <description>容器执行器类</description>
    </property>
    
    <!-- 虚拟内存检查 -->
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
        <description>禁用虚拟内存检查</description>
    </property>
    
    <!-- 物理内存检查 -->
    <property>
        <name>yarn.nodemanager.pmem-check-enabled</name>
        <value>true</value>
        <description>启用物理内存检查</description>
    </property>
</configuration>
