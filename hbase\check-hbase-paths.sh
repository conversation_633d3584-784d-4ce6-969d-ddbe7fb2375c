#!/bin/bash

# HBase路径快速检查脚本

echo "=== HBase路径快速检查 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo ""
echo "检查所有HBase相关路径配置..."

# 检查配置文件中的路径
echo ""
echo "1. 检查配置文件中的路径引用..."

# 检查所有包含 /export/server/hbase/bin 的文件（应该都是 /export/server/hbase/hbase/bin）
echo "  搜索可能错误的路径引用..."
wrong_paths=$(grep -r "/export/server/hbase/bin" hbase/ 2>/dev/null | grep -v "/export/server/hbase/hbase/bin")

if [ -n "$wrong_paths" ]; then
    echo -e "  ${RED}✗ 发现错误的路径引用:${NC}"
    echo "$wrong_paths" | sed 's/^/    /'
else
    echo -e "  ${GREEN}✓ 未发现错误的路径引用${NC}"
fi

# 检查正确的路径引用
echo ""
echo "  检查正确的路径引用..."
correct_paths=$(grep -r "/export/server/hbase/hbase/bin" hbase/ 2>/dev/null | wc -l)
echo -e "  ${GREEN}✓ 找到 $correct_paths 处正确的路径引用${NC}"

# 检查配置文件中的关键路径
echo ""
echo "2. 检查关键配置文件..."

files_to_check=(
    "hbase/conf/hbase-env.sh"
    "hbase/start-hbase.sh" 
    "hbase/stop-hbase.sh"
    "hbase/README.md"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "  检查 $file..."
        
        # 检查是否包含正确的路径
        if grep -q "/export/server/hbase/hbase" "$file"; then
            echo -e "    ${GREEN}✓ 包含正确的路径引用${NC}"
        else
            echo -e "    ${YELLOW}⚠ 未找到路径引用${NC}"
        fi
        
        # 检查是否包含错误的路径
        wrong_in_file=$(grep "/export/server/hbase/bin\|/export/server/hbase/conf" "$file" 2>/dev/null | grep -v "/export/server/hbase/hbase")
        if [ -n "$wrong_in_file" ]; then
            echo -e "    ${RED}✗ 发现错误路径:${NC}"
            echo "$wrong_in_file" | sed 's/^/      /'
        fi
    else
        echo -e "  ${RED}✗ 文件不存在: $file${NC}"
    fi
done

echo ""
echo "3. 验证实际目录结构..."

# 检查实际的目录结构
if [ -d "/export/server/hbase" ]; then
    echo -e "  ${GREEN}✓ /export/server/hbase 存在${NC}"
    
    if [ -d "/export/server/hbase/hbase" ]; then
        echo -e "  ${GREEN}✓ /export/server/hbase/hbase 存在${NC}"
        
        # 检查关键子目录
        subdirs=("bin" "conf" "lib")
        for subdir in "${subdirs[@]}"; do
            if [ -d "/export/server/hbase/hbase/$subdir" ]; then
                echo -e "    ${GREEN}✓ /export/server/hbase/hbase/$subdir 存在${NC}"
            else
                echo -e "    ${RED}✗ /export/server/hbase/hbase/$subdir 不存在${NC}"
            fi
        done
    else
        echo -e "  ${RED}✗ /export/server/hbase/hbase 不存在${NC}"
        echo "    请确认HBase是否正确安装"
    fi
else
    echo -e "  ${RED}✗ /export/server/hbase 不存在${NC}"
fi

echo ""
echo "4. 生成路径配置总结..."

echo ""
echo "正确的HBase路径配置应该是:"
echo "  HBASE_HOME=/export/server/hbase/hbase"
echo "  HBASE_CONF_DIR=/export/server/hbase/hbase/conf"
echo "  HBase启动脚本: /export/server/hbase/hbase/bin/hbase-daemon.sh"
echo "  HBase Shell: /export/server/hbase/hbase/bin/hbase shell"

echo ""
echo "如果发现路径错误，请运行以下命令修正:"
echo "  1. 检查实际安装目录: ls -la /export/server/hbase/"
echo "  2. 确认HBase安装位置: ls -la /export/server/hbase/hbase/"
echo "  3. 重新运行路径验证: ./hbase/verify-hbase-paths.sh"

echo ""
echo "=== 路径检查完成 ==="
